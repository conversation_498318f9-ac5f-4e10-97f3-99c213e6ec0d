import { Injectable } from '@nestjs/common';
import { UserProduct } from '@modules/business/entities/user-product.entity';
import { ProductTypeEnum, EntityStatusEnum } from '@modules/business/enums';
import { PhysicalProductCreateDto } from '../../../dto/create/physical-product.dto';
import { PhysicalProductResponseDto } from '../../../dto/response/physical-product-response.dto';
import { BaseProductHandler, ProductCreationContext, ProductCreationResult } from './base-product.handler';
import { PhysicalProductMapper } from '../mappers/physical-product.mapper';

/**
 * Handler cho Physical Product
 * Xử lý logic riêng cho sản phẩm vật lý
 */
@Injectable()
export class PhysicalProductHandler extends BaseProductHandler<
  PhysicalProductCreateDto,
  PhysicalProductResponseDto
> {
  readonly productType = ProductTypeEnum.PHYSICAL;

  constructor(
    private readonly physicalProductMapper: PhysicalProductMapper,
    // Inject các services cần thiết
    // private readonly inventoryService: InventoryService,
    // private readonly classificationService: ClassificationService,
    // private readonly imageService: ImageService,
  ) {
    super();
  }

  /**
   * Validate dữ liệu đầu vào cho physical product
   */
  async validate(dto: PhysicalProductCreateDto, userId: number): Promise<void> {
    // Validate common fields
    this.validateCommonFields(dto);

    // Validate physical-specific fields
    if (!dto.inventory) {
      throw new Error('Inventory is required for physical products');
    }

    if (!dto.price) {
      throw new Error('Price is required for physical products');
    }

    // Validate inventory data
    if (dto.inventory.availableQuantity !== undefined && dto.inventory.availableQuantity < 0) {
      throw new Error('Available quantity cannot be negative');
    }

    if (dto.inventory.warehouseId !== undefined && dto.inventory.warehouseId <= 0) {
      throw new Error('Warehouse ID must be positive');
    }
  }

  /**
   * Tạo base product entity cho physical product
   */
  async createBaseProduct(dto: PhysicalProductCreateDto, userId: number): Promise<UserProduct> {
    const { customFields, metadata } = await this.processCustomFields(dto);

    const product = new UserProduct();
    product.name = dto.name;
    product.productType = ProductTypeEnum.PHYSICAL;
    product.price = dto.price;
    product.typePrice = dto.typePrice;
    product.description = dto.description || '';
    product.images = []; // Sẽ được cập nhật sau khi xử lý images
    product.tags = dto.tags || [];
    product.metadata = metadata;
    product.createdBy = userId;
    product.createdAt = Date.now();
    product.updatedAt = Date.now();
    product.status = EntityStatusEnum.PENDING;

    // Set embedding fields to null
    product.nameEmbedding = null;
    product.descriptionEmbedding = null;
    product.tagsEmbedding = null;

    // Set shipment config
    product.shipmentConfig = this.getShipmentConfig(dto);

    return product;
  }

  /**
   * Xử lý inventory cho physical product
   */
  async processInventory(
    product: UserProduct,
    dto: PhysicalProductCreateDto,
    context: ProductCreationContext
  ): Promise<any> {
    // Logic xử lý inventory
    // Có thể sử dụng existing inventory hoặc tạo mới
    if (dto.inventory.inventoryId) {
      // Sử dụng inventory có sẵn
      return this.useExistingInventory(product.id, dto.inventory.inventoryId, context.userId);
    } else {
      // Tạo inventory mới
      return this.createNewInventory(product.id, dto.inventory, context.userId);
    }
  }

  /**
   * Xử lý advanced info cho physical product (không cần thiết cho physical)
   */
  async processAdvancedInfo(
    product: UserProduct,
    dto: PhysicalProductCreateDto,
    context: ProductCreationContext
  ): Promise<any> {
    // Physical products don't have advanced info
    return null;
  }

  /**
   * Xử lý classifications cho physical product
   */
  async processClassifications(
    productId: number,
    dto: PhysicalProductCreateDto,
    context: ProductCreationContext
  ): Promise<any[]> {
    if (!dto.classifications || dto.classifications.length === 0) {
      return [];
    }

    // Logic tạo classifications
    const classifications = [];
    for (const classificationDto of dto.classifications) {
      // const classification = await this.classificationService.create(productId, classificationDto, context.userId);
      // classifications.push(classification);
    }

    return classifications;
  }

  /**
   * Xử lý images upload URLs
   */
  async processImages(dto: PhysicalProductCreateDto): Promise<{
    imageEntries: any[];
    imagesUploadUrls: any[];
  }> {
    const imageEntries: any[] = [];
    const imagesUploadUrls: any[] = [];
    const now = Date.now();

    if (dto.imagesMediaTypes && dto.imagesMediaTypes.length > 0) {
      for (let i = 0; i < dto.imagesMediaTypes.length; i++) {
        const mediaType = dto.imagesMediaTypes[i];
        const fileName = `product-image-${i}-${now}`;

        // Logic tạo presigned URL
        // const imageUploadUrl = await this.imageService.createUploadUrl(fileName, mediaType);

        const key = `${fileName}.${this.getFileExtension(mediaType)}`;
        const url = `https://presigned-url-example.com/${key}`;

        imageEntries.push({
          key: key,
          position: i
        });

        imagesUploadUrls.push({
          url: url,
          key: key,
          index: i
        });
      }
    }

    return { imageEntries, imagesUploadUrls };
  }

  /**
   * Xử lý advanced images upload URLs cho physical product (không cần thiết cho physical)
   */
  async processAdvancedImages(dto: PhysicalProductCreateDto): Promise<{
    advancedImagesUploadUrls: any[];
  }> {
    // Physical products don't have advanced images
    return { advancedImagesUploadUrls: [] };
  }

  /**
   * Map entity sang response DTO
   */
  async mapToResponseDto(
    product: UserProduct,
    result: ProductCreationResult
  ): Promise<PhysicalProductResponseDto> {
    return this.physicalProductMapper.mapToResponseDto(product, result);
  }

  /**
   * Private helper methods
   */
  private async useExistingInventory(productId: number, inventoryId: number, userId: number): Promise<any> {
    // Logic sử dụng inventory có sẵn
    return {
      id: inventoryId,
      productId: productId,
      // ... other inventory fields
    };
  }

  private async createNewInventory(productId: number, inventoryData: any, userId: number): Promise<any> {
    // Logic tạo inventory mới
    return {
      id: Date.now(), // Mock ID
      productId: productId,
      warehouseId: inventoryData.warehouseId,
      currentQuantity: inventoryData.availableQuantity || 0,
      totalQuantity: inventoryData.availableQuantity || 0,
      availableQuantity: inventoryData.availableQuantity || 0,
      reservedQuantity: 0,
      defectiveQuantity: 0,
      sku: inventoryData.sku,
      barcode: inventoryData.barcode,
      lastUpdated: Date.now()
    };
  }

  private getFileExtension(mediaType: string): string {
    const extensions: { [key: string]: string } = {
      'image/jpeg': 'jpg',
      'image/jpg': 'jpg',
      'image/png': 'png',
      'image/gif': 'gif',
      'image/webp': 'webp'
    };
    return extensions[mediaType] || 'jpg';
  }
}
