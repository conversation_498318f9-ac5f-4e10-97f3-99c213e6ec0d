import { Injectable } from '@nestjs/common';
import { UserProduct } from '@modules/business/entities/user-product.entity';
import { DigitalProductResponseDto, DigitalAdvancedInfoResponseDto } from '../../../dto/response/digital-product-response.dto';
import { ProductCreationResult } from '../handlers/base-product.handler';
import { BaseProductMapper } from './base-product.mapper';

/**
 * Mapper cho Digital Product
 * Chuyển đổi UserProduct entity sang DigitalProductResponseDto
 */
@Injectable()
export class DigitalProductMapper extends BaseProductMapper<DigitalProductResponseDto> {
  /**
   * Map UserProduct entity sang DigitalProductResponseDto
   */
  async mapToResponseDto(
    product: UserProduct,
    result: ProductCreationResult
  ): Promise<DigitalProductResponseDto> {
    // Map common fields
    const commonFields = this.mapCommonFields(product, result);

    // Map digital-specific fields
    const digitalResponse: DigitalProductResponseDto = {
      ...commonFields,
      productType: product.productType as any, // DIGITAL
      advancedInfo: this.mapDigitalAdvancedInfo(result.advancedInfo),
      shipmentConfig: {
        widthCm: 0,
        heightCm: 0,
        lengthCm: 0,
        weightGram: 0
      }
    } as DigitalProductResponseDto;

    // Transform using class-transformer
    return this.transform(DigitalProductResponseDto, digitalResponse);
  }

  /**
   * Map advanced info cho digital product
   */
  private mapDigitalAdvancedInfo(advancedInfo: any): DigitalAdvancedInfoResponseDto | undefined {
    if (!advancedInfo) {
      return undefined;
    }

    return {
      purchaseCount: advancedInfo.purchaseCount || 0,
      variants: this.mapDigitalVariants(advancedInfo.variants),
      images: advancedInfo.images || []
    };
  }

  /**
   * Map digital variants
   */
  private mapDigitalVariants(variants: any[]): any[] {
    if (!variants || variants.length === 0) {
      return [];
    }

    return variants.map(variant => ({
      id: variant.id,
      name: variant.name,
      sku: variant.sku,
      availableQuantity: variant.availableQuantity,
      minQuantityPerPurchase: variant.minQuantityPerPurchase,
      maxQuantityPerPurchase: variant.maxQuantityPerPurchase,
      price: variant.price,
      images: variant.images || [],
      description: variant.description,
      customFields: variant.customFields || []
    }));
  }
}
