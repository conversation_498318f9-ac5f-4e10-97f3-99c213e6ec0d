import { Injectable } from '@nestjs/common';
import { UserProduct } from '@modules/business/entities/user-product.entity';
import { ProductTypeEnum, EntityStatusEnum } from '@modules/business/enums';
import { ComboProductCreateDto } from '../../../dto/create/combo-product-create.dto';
import { ComboProductResponseDto } from '../../../dto/response/combo-product-response.dto';
import { BaseProductHandler, ProductCreationContext, ProductCreationResult } from './base-product.handler';
import { ComboProductMapper } from '../mappers/combo-product.mapper';

/**
 * Handler cho Combo Product
 * Xử lý logic riêng cho sản phẩm combo
 */
@Injectable()
export class ComboProductHandler extends BaseProductHandler<
  ComboProductCreateDto,
  ComboProductResponseDto
> {
  readonly productType = ProductTypeEnum.COMBO;

  constructor(
    private readonly comboProductMapper: ComboProductMapper,
    // Inject các services cần thiết
    // private readonly advancedInfoService: AdvancedInfoService,
    // private readonly classificationService: ClassificationService,
    // private readonly imageService: ImageService,
    // private readonly userProductRepository: UserProductRepository,
  ) {
    super();
  }

  /**
   * Validate dữ liệu đầu vào cho combo product
   */
  async validate(dto: ComboProductCreateDto, userId: number): Promise<void> {
    // Validate common fields
    this.validateCommonFields(dto);

    // Validate combo-specific fields
    if (!dto.advancedInfo) {
      throw new Error('Advanced info is required for combo products');
    }

    if (!dto.price) {
      throw new Error('Price is required for combo products');
    }

    const comboInfo = dto.advancedInfo;

    // Validate combo items
    if (!comboInfo.info || comboInfo.info.length === 0) {
      throw new Error('At least one product is required in combo');
    }

    if (comboInfo.info.length < 2) {
      throw new Error('Combo must contain at least 2 products');
    }

    for (const comboItem of comboInfo.info) {
      if (!comboItem.productId || comboItem.productId <= 0) {
        throw new Error('Valid product ID is required for combo item');
      }

      if (!comboItem.total || comboItem.total <= 0) {
        throw new Error('Combo item quantity must be positive');
      }
    }

    // Validate no duplicate products
    const productIds = comboInfo.info.map(item => item.productId);
    const uniqueProductIds = new Set(productIds);
    if (productIds.length !== uniqueProductIds.size) {
      throw new Error('Combo cannot contain duplicate products');
    }

    // Validate combo products exist and are available
    await this.validateComboProducts(comboInfo.info, userId);

    // Validate purchase count
    if (comboInfo.purchaseCount !== undefined && comboInfo.purchaseCount < 0) {
      throw new Error('Purchase count cannot be negative');
    }
  }

  /**
   * Tạo base product entity cho combo product
   */
  async createBaseProduct(dto: ComboProductCreateDto, userId: number): Promise<UserProduct> {
    const { customFields, metadata } = await this.processCustomFields(dto);

    const product = new UserProduct();
    product.name = dto.name;
    product.productType = ProductTypeEnum.COMBO;
    product.price = dto.price;
    product.typePrice = dto.typePrice;
    product.description = dto.description || '';
    product.images = []; // Sẽ được cập nhật sau khi xử lý images
    product.tags = dto.tags || [];
    product.metadata = metadata;
    product.createdBy = userId;
    product.createdAt = Date.now();
    product.updatedAt = Date.now();
    product.status = EntityStatusEnum.PENDING;

    // Set embedding fields to null
    product.nameEmbedding = null;
    product.descriptionEmbedding = null;
    product.tagsEmbedding = null;

    // Combo products have calculated shipment config
    product.shipmentConfig = await this.calculateComboShipmentConfig(dto.advancedInfo.info);

    return product;
  }

  /**
   * Xử lý advanced info cho combo product
   */
  async processAdvancedInfo(
    product: UserProduct,
    dto: ComboProductCreateDto,
    context: ProductCreationContext
  ): Promise<any> {
    // Lấy thông tin chi tiết các sản phẩm trong combo
    const comboItemsWithDetails = await this.enrichComboItemsWithProductDetails(dto.advancedInfo.info);

    // Tính toán giá và thông tin tiết kiệm
    const calculatedPrice = this.calculateComboPrice(comboItemsWithDetails, dto.price);
    const savingInfo = this.calculateSavingInfo(calculatedPrice);

    // Logic tạo advanced info cho combo product
    const advancedInfo = {
      productId: product.id,
      productType: ProductTypeEnum.COMBO,
      purchaseCount: dto.advancedInfo.purchaseCount || 0,
      info: comboItemsWithDetails,
      images: [], // Sẽ được cập nhật sau khi xử lý advanced images
      calculatedPrice: calculatedPrice,
      savingInfo: savingInfo,
      createdAt: context.timestamp,
      updatedAt: context.timestamp
    };

    // Logic lưu vào database
    // const savedAdvancedInfo = await this.advancedInfoService.create(advancedInfo);
    
    return advancedInfo;
  }

  /**
   * Xử lý inventory cho combo product (không cần thiết cho combo)
   */
  async processInventory(
    product: UserProduct,
    dto: ComboProductCreateDto,
    context: ProductCreationContext
  ): Promise<any> {
    // Combo products don't have direct inventory
    // Inventory is managed by individual products in the combo
    return null;
  }

  /**
   * Xử lý classifications cho combo product
   */
  async processClassifications(
    productId: number,
    dto: ComboProductCreateDto,
    context: ProductCreationContext
  ): Promise<any[]> {
    if (!dto.classifications || dto.classifications.length === 0) {
      return [];
    }

    // Logic tạo classifications
    const classifications = [];
    for (const classificationDto of dto.classifications) {
      // const classification = await this.classificationService.create(productId, classificationDto, context.userId);
      // classifications.push(classification);
    }

    return classifications;
  }

  /**
   * Xử lý advanced images upload URLs cho combo product
   */
  async processAdvancedImages(dto: ComboProductCreateDto): Promise<{
    advancedImagesUploadUrls: any[];
  }> {
    // Combo products don't have advanced images at the product level
    // Advanced images are handled by individual products in the combo
    return { advancedImagesUploadUrls: [] };
  }

  /**
   * Xử lý images upload URLs
   */
  async processImages(dto: ComboProductCreateDto): Promise<{
    imageEntries: any[];
    imagesUploadUrls: any[];
  }> {
    const imageEntries: any[] = [];
    const imagesUploadUrls: any[] = [];
    const now = Date.now();

    if (dto.imagesMediaTypes && dto.imagesMediaTypes.length > 0) {
      for (let i = 0; i < dto.imagesMediaTypes.length; i++) {
        const mediaType = dto.imagesMediaTypes[i];
        const fileName = `combo-product-image-${i}-${now}`;

        // Logic tạo presigned URL
        const key = `${fileName}.${this.getFileExtension(mediaType)}`;
        const url = `https://presigned-url-example.com/${key}`;

        imageEntries.push({
          key: key,
          position: i
        });

        imagesUploadUrls.push({
          url: url,
          key: key,
          index: i
        });
      }
    }

    return { imageEntries, imagesUploadUrls };
  }

  /**
   * Map entity sang response DTO
   */
  async mapToResponseDto(
    product: UserProduct,
    result: ProductCreationResult
  ): Promise<ComboProductResponseDto> {
    return this.comboProductMapper.mapToResponseDto(product, result);
  }

  /**
   * Private helper methods
   */
  private async validateComboProducts(comboItems: any[], userId: number): Promise<void> {
    for (const item of comboItems) {
      // Logic validate sản phẩm tồn tại và available
      // const product = await this.userProductRepository.findById(item.productId);
      // if (!product) {
      //   throw new Error(`Product with ID ${item.productId} not found`);
      // }
      // if (product.createdBy !== userId) {
      //   throw new Error(`Product with ID ${item.productId} does not belong to user`);
      // }
      // if (product.status !== EntityStatusEnum.ACTIVE) {
      //   throw new Error(`Product with ID ${item.productId} is not active`);
      // }
    }
  }

  private async enrichComboItemsWithProductDetails(comboItems: any[]): Promise<any[]> {
    const enrichedItems: any[] = [];

    for (const item of comboItems) {
      // Logic lấy thông tin chi tiết sản phẩm
      // const productDetails = await this.userProductRepository.findById(item.productId);

      // Mock product details
      const productDetails = {
        id: item.productId,
        name: `Product ${item.productId}`,
        productType: 'PHYSICAL',
        price: {
          listPrice: 100000,
          salePrice: 80000,
          currency: 'VND'
        },
        images: [],
        status: 'ACTIVE',
        availableQuantity: 100
      };

      enrichedItems.push({
        productId: item.productId,
        total: item.total,
        productDetails: productDetails
      });
    }

    return enrichedItems;
  }

  private calculateComboPrice(comboItemsWithDetails: any[], comboPrice: any): any {
    let originalTotalPrice = 0;

    // Tính tổng giá gốc từ các sản phẩm con
    for (const item of comboItemsWithDetails) {
      const productPrice = item.productDetails?.price?.salePrice || 0;
      originalTotalPrice += productPrice * item.total;
    }

    const finalTotalPrice = comboPrice?.salePrice || comboPrice?.listPrice || 0;
    const discountAmount = originalTotalPrice - finalTotalPrice;

    return {
      originalTotalPrice: originalTotalPrice,
      discountAmount: Math.max(0, discountAmount),
      finalTotalPrice: finalTotalPrice,
      currency: comboPrice?.currency || 'VND'
    };
  }

  private calculateSavingInfo(calculatedPrice: any): any {
    if (calculatedPrice.discountAmount <= 0) {
      return {
        savingAmount: 0,
        savingPercentage: 0,
        currency: calculatedPrice.currency
      };
    }

    const savingPercentage = (calculatedPrice.discountAmount / calculatedPrice.originalTotalPrice) * 100;

    return {
      savingAmount: calculatedPrice.discountAmount,
      savingPercentage: Math.round(savingPercentage * 100) / 100, // Round to 2 decimal places
      currency: calculatedPrice.currency
    };
  }

  private async calculateComboShipmentConfig(comboItems: any[]): Promise<any> {
    // Logic tính toán shipment config từ các sản phẩm con
    // Có thể lấy max của từng dimension hoặc tính tổng weight
    
    // Mock calculation
    const totalWeight = comboItems.length * 200; // 200g per item
    const maxDimension = Math.max(25, comboItems.length * 5);

    return {
      widthCm: maxDimension,
      heightCm: Math.min(maxDimension, 50),
      lengthCm: maxDimension,
      weightGram: totalWeight
    };
  }

  private getFileExtension(mediaType: string): string {
    const extensions: { [key: string]: string } = {
      'image/jpeg': 'jpg',
      'image/jpg': 'jpg',
      'image/png': 'png',
      'image/gif': 'gif',
      'image/webp': 'webp'
    };
    return extensions[mediaType] || 'jpg';
  }
}
