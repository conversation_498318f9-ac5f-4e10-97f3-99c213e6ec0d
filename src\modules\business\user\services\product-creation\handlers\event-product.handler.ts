import { Injectable } from '@nestjs/common';
import { UserProduct } from '@modules/business/entities/user-product.entity';
import { ProductTypeEnum, EntityStatusEnum, PriceTypeEnum } from '@modules/business/enums';
import { EventProductCreateDto } from '../../../dto/create/event-product-create.dto';
import { EventProductResponseDto } from '../../../dto/response/event-product-response.dto';
import { BaseProductHandler, ProductCreationContext, ProductCreationResult } from './base-product.handler';
import { EventProductMapper } from '../mappers/event-product.mapper';

/**
 * Handler cho Event Product
 * Xử lý logic riêng cho sản phẩm sự kiện
 */
@Injectable()
export class EventProductHandler extends BaseProductHandler<
  EventProductCreateDto,
  EventProductResponseDto
> {
  readonly productType = ProductTypeEnum.EVENT;

  constructor(
    private readonly eventProductMapper: EventProductMapper,
    // Inject các services cần thiết
    // private readonly advancedInfoService: AdvancedInfoService,
    // private readonly classificationService: ClassificationService,
    // private readonly imageService: ImageService,
  ) {
    super();
  }

  /**
   * Validate dữ liệu đầu vào cho event product
   */
  async validate(dto: EventProductCreateDto, userId: number): Promise<void> {
    // Validate common fields
    this.validateCommonFields(dto);

    // Validate event-specific fields
    if (!dto.advancedInfo) {
      throw new Error('Advanced info is required for event products');
    }

    const eventInfo = dto.advancedInfo;

    // Validate event format
    if (!eventInfo.eventFormat) {
      throw new Error('Event format is required');
    }

    // Validate event dates
    if (!eventInfo.startDate || !eventInfo.endDate) {
      throw new Error('Event start date and end date are required');
    }

    if (eventInfo.startDate >= eventInfo.endDate) {
      throw new Error('Event start date must be before end date');
    }

    if (eventInfo.startDate < Date.now()) {
      throw new Error('Event start date cannot be in the past');
    }

    // Validate timezone
    if (!eventInfo.timezone) {
      throw new Error('Event timezone is required');
    }

    // Validate event location based on format
    if (eventInfo.eventFormat === 'ONLINE' && !eventInfo.eventLink) {
      throw new Error('Event link is required for online events');
    }

    if (eventInfo.eventFormat === 'OFFLINE' && !eventInfo.eventLocation) {
      throw new Error('Event location is required for offline events');
    }

    if (eventInfo.eventFormat === 'HYBRID' && (!eventInfo.eventLink || !eventInfo.eventLocation)) {
      throw new Error('Both event link and location are required for hybrid events');
    }

    // Validate ticket types if present
    if (eventInfo.ticketTypes && eventInfo.ticketTypes.length > 0) {
      for (const ticketType of eventInfo.ticketTypes) {
        if (!ticketType.name || ticketType.name.trim().length === 0) {
          throw new Error('Ticket type name is required');
        }

        if (ticketType.price !== undefined && ticketType.price < 0) {
          throw new Error('Ticket type price cannot be negative');
        }

        if (!ticketType.startTime || !ticketType.endTime) {
          throw new Error('Ticket type start time and end time are required');
        }

        if (ticketType.startTime >= ticketType.endTime) {
          throw new Error('Ticket type start time must be before end time');
        }

        if (ticketType.quantity !== undefined && ticketType.quantity < 0) {
          throw new Error('Ticket type quantity cannot be negative');
        }

        if (ticketType.minQuantityPerPurchase !== undefined && ticketType.minQuantityPerPurchase < 0) {
          throw new Error('Ticket type minimum quantity per purchase cannot be negative');
        }

        if (ticketType.maxQuantityPerPurchase !== undefined && ticketType.maxQuantityPerPurchase < 0) {
          throw new Error('Ticket type maximum quantity per purchase cannot be negative');
        }

        if (ticketType.minQuantityPerPurchase !== undefined && 
            ticketType.maxQuantityPerPurchase !== undefined && 
            ticketType.minQuantityPerPurchase > ticketType.maxQuantityPerPurchase) {
          throw new Error('Ticket type minimum quantity cannot be greater than maximum quantity');
        }
      }
    }
  }

  /**
   * Tạo base product entity cho event product
   */
  async createBaseProduct(dto: EventProductCreateDto, userId: number): Promise<UserProduct> {
    const { customFields, metadata } = await this.processCustomFields(dto);

    const product = new UserProduct();
    product.name = dto.name;
    product.productType = ProductTypeEnum.EVENT;
    
    // Event products có thể có giá null (giá lấy từ ticket types)
    product.price = dto.price || null;
    product.typePrice = dto.typePrice || PriceTypeEnum.HAS_PRICE;
    
    product.description = dto.description || '';
    product.images = []; // Sẽ được cập nhật sau khi xử lý images
    product.tags = dto.tags || [];
    product.metadata = metadata;
    product.createdBy = userId;
    product.createdAt = Date.now();
    product.updatedAt = Date.now();
    product.status = EntityStatusEnum.PENDING;

    // Set embedding fields to null
    product.nameEmbedding = null;
    product.descriptionEmbedding = null;
    product.tagsEmbedding = null;

    // Event products have zero shipment config
    product.shipmentConfig = {
      widthCm: 0,
      heightCm: 0,
      lengthCm: 0,
      weightGram: 0
    };

    return product;
  }

  /**
   * Xử lý advanced info cho event product
   */
  async processAdvancedInfo(
    product: UserProduct,
    dto: EventProductCreateDto,
    context: ProductCreationContext
  ): Promise<any> {
    // Tính toán giá cuối cùng từ ticket types nếu không có giá chính
    const finalPrice = this.calculateEventPrice(dto.advancedInfo, product.price);
    
    // Cập nhật giá sản phẩm nếu cần
    if (!product.price && finalPrice) {
      product.price = finalPrice;
    }

    // Logic tạo advanced info cho event product
    const advancedInfo = {
      productId: product.id,
      productType: ProductTypeEnum.EVENT,
      purchaseCount: dto.advancedInfo.purchaseCount || 0,
      eventFormat: dto.advancedInfo.eventFormat,
      eventLink: dto.advancedInfo.eventLink,
      eventLocation: dto.advancedInfo.eventLocation,
      startDate: dto.advancedInfo.startDate,
      endDate: dto.advancedInfo.endDate,
      timezone: dto.advancedInfo.timezone,
      ticketTypes: dto.advancedInfo.ticketTypes || [],
      images: [], // Sẽ được cập nhật sau khi xử lý advanced images
      createdAt: context.timestamp,
      updatedAt: context.timestamp
    };

    // Logic lưu vào database
    // const savedAdvancedInfo = await this.advancedInfoService.create(advancedInfo);
    
    return advancedInfo;
  }

  /**
   * Xử lý inventory cho event product (không cần thiết cho event)
   */
  async processInventory(
    product: UserProduct,
    dto: EventProductCreateDto,
    context: ProductCreationContext
  ): Promise<any> {
    // Event products don't have traditional inventory
    // Inventory is managed through ticket types
    return null;
  }

  /**
   * Xử lý classifications cho event product
   */
  async processClassifications(
    productId: number,
    dto: EventProductCreateDto,
    context: ProductCreationContext
  ): Promise<any[]> {
    if (!dto.classifications || dto.classifications.length === 0) {
      return [];
    }

    // Logic tạo classifications
    const classifications = [];
    for (const classificationDto of dto.classifications) {
      // const classification = await this.classificationService.create(productId, classificationDto, context.userId);
      // classifications.push(classification);
    }

    return classifications;
  }

  /**
   * Xử lý images upload URLs
   */
  async processImages(dto: EventProductCreateDto): Promise<{
    imageEntries: any[];
    imagesUploadUrls: any[];
  }> {
    const imageEntries: any[] = [];
    const imagesUploadUrls: any[] = [];
    const now = Date.now();

    if (dto.imagesMediaTypes && dto.imagesMediaTypes.length > 0) {
      for (let i = 0; i < dto.imagesMediaTypes.length; i++) {
        const mediaType = dto.imagesMediaTypes[i];
        const fileName = `event-product-image-${i}-${now}`;

        // Logic tạo presigned URL
        const key = `${fileName}.${this.getFileExtension(mediaType)}`;
        const url = `https://presigned-url-example.com/${key}`;

        imageEntries.push({
          key: key,
          position: i
        });

        imagesUploadUrls.push({
          url: url,
          key: key,
          index: i
        });
      }
    }

    return { imageEntries, imagesUploadUrls };
  }

  /**
   * Xử lý advanced images upload URLs
   */
  async processAdvancedImages(dto: EventProductCreateDto): Promise<{
    advancedImagesUploadUrls: any[];
  }> {
    const advancedImagesUploadUrls: any[] = [];
    const now = Date.now();

    // Xử lý ticket type images
    if (dto.advancedInfo.ticketTypes && dto.advancedInfo.ticketTypes.length > 0) {
      for (let ticketIndex = 0; ticketIndex < dto.advancedInfo.ticketTypes.length; ticketIndex++) {
        const ticketType = dto.advancedInfo.ticketTypes[ticketIndex];
        
        if (ticketType.imagesMediaTypes && ticketType.imagesMediaTypes.length > 0) {
          for (let imageIndex = 0; imageIndex < ticketType.imagesMediaTypes.length; imageIndex++) {
            const mediaType = ticketType.imagesMediaTypes[imageIndex];
            const fileName = `event-ticket-${ticketIndex}-image-${imageIndex}-${now}`;

            const key = `${fileName}.${this.getFileExtension(mediaType)}`;
            const url = `https://presigned-url-example.com/${key}`;

            advancedImagesUploadUrls.push({
              url: url,
              key: key,
              type: 'ticketType',
              index: imageIndex,
              position: ticketIndex
            });
          }
        }
      }
    }

    return { advancedImagesUploadUrls };
  }

  /**
   * Map entity sang response DTO
   */
  async mapToResponseDto(
    product: UserProduct,
    result: ProductCreationResult
  ): Promise<EventProductResponseDto> {
    return this.eventProductMapper.mapToResponseDto(product, result);
  }

  /**
   * Private helper methods
   */
  private calculateEventPrice(eventInfo: any, currentPrice: any): any {
    // Nếu đã có giá, giữ nguyên
    if (currentPrice) {
      return currentPrice;
    }

    // Tính giá từ ticket types
    if (eventInfo.ticketTypes && eventInfo.ticketTypes.length > 0) {
      const prices = eventInfo.ticketTypes
        .filter((ticket: any) => ticket.price !== undefined && ticket.price !== null)
        .map((ticket: any) => ticket.price);

      if (prices.length > 0) {
        const minPrice = Math.min(...prices);
        const maxPrice = Math.max(...prices);

        return {
          listPrice: maxPrice,
          salePrice: minPrice,
          currency: 'VND'
        };
      }
    }

    return null;
  }

  private getFileExtension(mediaType: string): string {
    const extensions: { [key: string]: string } = {
      'image/jpeg': 'jpg',
      'image/jpg': 'jpg',
      'image/png': 'png',
      'image/gif': 'gif',
      'image/webp': 'webp'
    };
    return extensions[mediaType] || 'jpg';
  }
}
