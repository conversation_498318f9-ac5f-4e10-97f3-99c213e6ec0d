import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import {
  BusinessUpdateProductDto,
  BusinessProductResponseDto as ProductResponseDto,
} from '../../dto';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { UpdateProductProcessor } from './update-product.processor';

/**
 * Orchestrator chính cho việc cập nhật sản phẩm
 * Điều phối toàn bộ luồng update từ các processor nhỏ
 */
@Injectable()
export class UpdateProductOrchestrator {
  private readonly logger = new Logger(UpdateProductOrchestrator.name);

  constructor(
    private readonly updateProcessor: UpdateProductProcessor,
  ) {}

  /**
   * Method chính để cập nhật sản phẩm
   * Thay thế cho method updateProduct cũ trong UserProductService
   */
  @Transactional()
  async updateProduct(
    id: number,
    updateProductDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<ProductResponseDto> {
    try {
      this.logger.log(`Bắt đầu cập nhật sản phẩm ID: ${id} cho user: ${userId}`);

      // BƯỚC 1: Tìm và validate sản phẩm
      const product = await this.updateProcessor.findAndValidateProduct(id, userId);

      // BƯỚC 2: Cập nhật thông tin cơ bản
      this.updateProcessor.updateBasicFields(product, updateProductDto);

      // BƯỚC 3: Cập nhật giá sản phẩm
      this.updateProcessor.updateProductPricing(product, updateProductDto);

      // BƯỚC 4: Xử lý hình ảnh sản phẩm chính
      const imagesUploadUrls = await this.processMainProductImages(updateProductDto);

      // BƯỚC 5: Cập nhật custom fields và metadata
      await this.updateProcessor.updateCustomFields(product, updateProductDto);

      // BƯỚC 6: Finalize và lưu sản phẩm
      this.updateProcessor.finalizeProductUpdate(product);
      const updatedProduct = await this.updateProcessor.saveUpdatedProduct(product);

      // BƯỚC 7: Xử lý advanced info
      const { advancedImagesUploadUrls } = await this.updateProcessor.processAdvancedInfoUpdate(
        updatedProduct,
        updateProductDto,
        Date.now()
      );

      // BƯỚC 8: Xử lý image operations cho advanced info (nếu không có advancedInfo trong request)
      await this.processAdvancedImageOperations(updatedProduct, updateProductDto);

      // BƯỚC 9: Xử lý classifications
      const { classifications, classificationUploadUrls } = await this.updateProcessor.processClassificationsUpdate(
        id,
        updateProductDto,
        userId
      );

      // BƯỚC 10: Xử lý xóa classifications
      await this.updateProcessor.processClassificationsDeletion(
        id,
        updateProductDto.classificationsToDelete || [],
        userId
      );

      // BƯỚC 11: Validate và xử lý inventory
      await this.updateProcessor.validatePhysicalProductInventory(updatedProduct, updateProductDto);
      const inventory = await this.updateProcessor.processInventoryUpdate(updatedProduct, updateProductDto, userId);

      // BƯỚC 12: Xây dựng response cuối cùng
      const response = await this.updateProcessor.buildUpdateResponse(
        updatedProduct,
        imagesUploadUrls,
        advancedImagesUploadUrls,
        classificationUploadUrls,
        classifications,
        inventory
      );

      this.logger.log(`Hoàn thành cập nhật sản phẩm ID: ${id}`);
      return response;

    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật sản phẩm ID: ${id}`, error.stack);

      // Nếu là AppException, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Wrap lỗi khác thành AppException
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_UPDATE_FAILED,
        `Lỗi khi cập nhật sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Xử lý hình ảnh sản phẩm chính
   */
  private async processMainProductImages(updateProductDto: BusinessUpdateProductDto): Promise<any[]> {
    const imagesUploadUrls: any[] = [];
    const now = Date.now();

    // TODO: Implement logic xử lý hình ảnh từ service gốc
    // Xử lý hình ảnh theo 3 cách: imagesMediaTypes, imageOperations, hoặc images (deprecated)
    // await this.processImageUpdates(updateProductDto, product, imagesUploadUrls, now);

    return imagesUploadUrls;
  }

  /**
   * Xử lý image operations cho advanced info khi không có advancedInfo trong request
   */
  private async processAdvancedImageOperations(
    product: any,
    updateProductDto: BusinessUpdateProductDto
  ): Promise<void> {
    // Xử lý imageOperations cho ticket types/service packages
    if (this.shouldProcessAdvancedImageOperations(product, updateProductDto)) {
      try {
        this.logger.log(`Xử lý imageOperations cho ${product.productType} product với ${updateProductDto.imageOperations?.length || 0} operations`);
        
        // TODO: Implement logic từ service gốc
        // await this.processAdvancedImageOperations(...)
        
      } catch (imageOperationsError) {
        this.logger.warn(`Không thể xử lý imageOperations cho sản phẩm ${product.id}: ${imageOperationsError.message}`);
      }
    }

    // Xử lý images (deprecated) cho ticket types/service packages
    if (this.shouldProcessAdvancedImages(product, updateProductDto)) {
      try {
        this.logger.log(`Xử lý images cho ${product.productType} advanced info với ${updateProductDto.images?.length || 0} operations`);
        
        // TODO: Implement logic từ service gốc
        // await this.processAdvancedImages(...)
        
      } catch (imagesError) {
        this.logger.warn(`Không thể xử lý images cho advanced info của sản phẩm ${product.id}: ${imagesError.message}`);
      }
    }
  }

  /**
   * Kiểm tra có nên xử lý advanced image operations không
   */
  private shouldProcessAdvancedImageOperations(product: any, updateProductDto: BusinessUpdateProductDto): boolean {
    return !updateProductDto.advancedInfo &&
           updateProductDto.imageOperations &&
           updateProductDto.imageOperations.length > 0 &&
           product.detail_id &&
           (product.productType === 'EVENT' || product.productType === 'SERVICE');
  }

  /**
   * Kiểm tra có nên xử lý advanced images không
   */
  private shouldProcessAdvancedImages(product: any, updateProductDto: BusinessUpdateProductDto): boolean {
    const isAdvancedImages = !(updateProductDto as any).isProductImageOperations;
    
    return !updateProductDto.advancedInfo &&
           updateProductDto.images &&
           updateProductDto.images.length > 0 &&
           product.detail_id &&
           (product.productType === 'EVENT' || product.productType === 'SERVICE') &&
           isAdvancedImages;
  }
}

/**
 * Interface cho kết quả xử lý update
 */
export interface UpdateProductResult {
  product: any;
  imagesUploadUrls: any[];
  advancedImagesUploadUrls: any[];
  classificationUploadUrls: any[];
  classifications: any[];
  inventory?: any;
}

/**
 * Interface cho context update
 */
export interface UpdateProductContext {
  productId: number;
  userId: number;
  updateDto: BusinessUpdateProductDto;
  timestamp: number;
}

/**
 * Enum cho các bước update
 */
export enum UpdateStep {
  VALIDATE_PRODUCT = 'validate_product',
  UPDATE_BASIC_FIELDS = 'update_basic_fields',
  UPDATE_PRICING = 'update_pricing',
  PROCESS_IMAGES = 'process_images',
  UPDATE_CUSTOM_FIELDS = 'update_custom_fields',
  SAVE_PRODUCT = 'save_product',
  PROCESS_ADVANCED_INFO = 'process_advanced_info',
  PROCESS_CLASSIFICATIONS = 'process_classifications',
  PROCESS_INVENTORY = 'process_inventory',
  BUILD_RESPONSE = 'build_response',
}

/**
 * Interface cho tracking progress
 */
export interface UpdateProgress {
  currentStep: UpdateStep;
  completedSteps: UpdateStep[];
  totalSteps: number;
  errors: string[];
  warnings: string[];
}
