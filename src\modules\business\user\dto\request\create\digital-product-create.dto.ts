import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsEnum,
  IsNotEmpty,
  IsObject,
  ValidateNested,
} from 'class-validator';
import { ProductTypeEnum } from '@modules/business/enums';
import { BaseProductDto } from '../base-product/base-product.dto';
import { ProductPriceDto } from '../product-price/product-price.type';
import { DigitalAdvancedInfoDto } from '../../advanced-info';
import { HasPriceDto } from '../../price.dto';

/**
 * DTO cho việc tạo sản phẩm số (DIGITAL)
 * <PERSON>ế thừa từ BaseAdvancedProductDto và thêm các trường đặc thù cho sản phẩm số
 */
export class DigitalProductCreateDto extends BaseProductDto {
  @ApiProperty({
    description: 'Thông tin nâng cao cho sản phẩm số',
    type: DigitalAdvancedInfoDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => DigitalAdvancedInfoDto)
  advancedInfo: DigitalAdvancedInfoDto;

  // Override price để bắt buộc cho DIGITAL
  @ApiProperty({
    description: 'Thông tin giá sản phẩm - Bắt buộc đối với sản phẩm số',
    oneOf: [
      { $ref: '#/components/schemas/HasPriceDto' },
      { $ref: '#/components/schemas/StringPriceDto' },
      { $ref: '#/components/schemas/ClassificationPriceDto' },
      { $ref: '#/components/schemas/ClassificationStringPriceDto' }
    ],
  })
  @IsNotEmpty()
  @IsObject()
  declare price: HasPriceDto; // Required for digital products
}
