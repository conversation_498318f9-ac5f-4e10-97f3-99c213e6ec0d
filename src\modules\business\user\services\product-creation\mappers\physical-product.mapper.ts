import { Injectable } from '@nestjs/common';
import { UserProduct } from '@modules/business/entities/user-product.entity';
import { PhysicalProductResponseDto } from '../../../dto/response/physical-product-response.dto';
import { ProductCreationResult } from '../handlers/base-product.handler';
import { BaseProductMapper } from './base-product.mapper';

/**
 * Mapper cho Physical Product
 * Chuyển đổi UserProduct entity sang PhysicalProductResponseDto
 */
@Injectable()
export class PhysicalProductMapper extends BaseProductMapper<PhysicalProductResponseDto> {
  /**
   * Map UserProduct entity sang PhysicalProductResponseDto
   */
  async mapToResponseDto(
    product: UserProduct,
    result: ProductCreationResult
  ): Promise<PhysicalProductResponseDto> {
    // Map common fields
    const commonFields = this.mapCommonFields(product, result);

    // Map physical-specific fields
    const physicalResponse: PhysicalProductResponseDto = {
      ...commonFields,
      productType: product.productType as any, // PHYSICAL
      inventory: this.mapInventory(result.inventory),
      shipmentConfig: product.shipmentConfig
    } as PhysicalProductResponseDto;

    // Transform using class-transformer
    return this.transform(PhysicalProductResponseDto, physicalResponse);
  }

  /**
   * Override mapInventory để map chi tiết cho physical product
   */
  protected mapInventory(inventory: any): any {
    if (!inventory) {
      return undefined;
    }

    return {
      id: inventory.id,
      warehouseId: inventory.warehouseId,
      availableQuantity: inventory.availableQuantity,
      minQuantity: inventory.minQuantity,
      maxQuantity: inventory.maxQuantity,
      sku: inventory.sku,
      barcode: inventory.barcode,
      unit: inventory.unit,
      createdAt: inventory.createdAt,
      updatedAt: inventory.updatedAt
    };
  }
}
