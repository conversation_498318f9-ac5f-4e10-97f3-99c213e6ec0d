import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsEnum,
  IsNotEmpty,
  IsObject,
  ValidateNested,
} from 'class-validator';
import { ProductTypeEnum } from '@modules/business/enums';
import { BaseProductDto } from '../base-product/base-product.dto';
import { ComboAdvancedInfoDto } from '../../advanced-info';
import { ProductPriceDto } from '../product-price/product-price.type';
import { HasPriceDto } from '../../price.dto';

/**
 * DTO cho việc tạo sản phẩm combo (COMBO)
 * Kế thừa từ BaseAdvancedProductDto và thêm các trường đặc thù cho combo
 */
export class ComboProductCreateDto extends BaseProductDto {
  @ApiProperty({
    description: 'Thông tin nâng cao cho sản phẩm combo',
    type: ComboAdvancedInfoDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => ComboAdvancedInfoDto)
  advancedInfo: ComboAdvancedInfoDto;

  // Override price để bắt buộc cho COMBO
  @ApiProperty({
    description: 'Thông tin giá sản phẩm - Bắt buộc đối với sản phẩm combo',
    oneOf: [
      { $ref: '#/components/schemas/HasPriceDto' },
      { $ref: '#/components/schemas/StringPriceDto' },
      { $ref: '#/components/schemas/ClassificationPriceDto' },
      { $ref: '#/components/schemas/ClassificationStringPriceDto' }
    ],
  })
  @IsNotEmpty()
  @IsObject()
  declare price: HasPriceDto; // Required for combo products
}
