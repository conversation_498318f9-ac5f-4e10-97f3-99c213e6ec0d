import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { ProductTypeEnum } from '@modules/business/enums';
import { BaseProductResponseDto } from '../base/base-product-response.dto';

/**
 * Response DTO cho inventory của sản phẩm vật lý
 */
export class PhysicalInventoryResponseDto {
  @ApiProperty({
    description: 'ID inventory',
    example: 1,
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'ID warehouse',
    example: 1,
  })
  @Expose()
  warehouseId: number;

  @ApiProperty({
    description: 'Số lượng có sẵn',
    example: 100,
  })
  @Expose()
  availableQuantity: number;

  @ApiProperty({
    description: 'Số lượng tối thiểu',
    example: 10,
  })
  @Expose()
  minQuantity: number;

  @ApiProperty({
    description: 'Số lượng tối đa',
    example: 1000,
  })
  @Expose()
  maxQuantity: number;

  @ApiProperty({
    description: 'SKU sản phẩm',
    example: 'SHIRT-001',
    required: false,
  })
  @Expose()
  sku?: string;

  @ApiProperty({
    description: 'Barcode sản phẩm',
    example: '1234567890123',
    required: false,
  })
  @Expose()
  barcode?: string;

  @ApiProperty({
    description: 'Đơn vị tính',
    example: 'cái',
  })
  @Expose()
  unit: string;

  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1704067200000,
  })
  @Expose()
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật cuối (timestamp)',
    example: 1704067200000,
  })
  @Expose()
  updatedAt: number;
}

/**
 * Response DTO cho sản phẩm vật lý (PHYSICAL)
 * Kế thừa từ BaseProductResponseDto và thêm các trường đặc thù cho sản phẩm vật lý
 */
export class PhysicalProductResponseDto extends BaseProductResponseDto {
  @ApiProperty({
    description: 'Loại sản phẩm - luôn là PHYSICAL',
    enum: ProductTypeEnum,
    example: ProductTypeEnum.PHYSICAL,
  })
  @Expose()
  declare productType: ProductTypeEnum.PHYSICAL;

  @ApiProperty({
    description: 'Thông tin tồn kho sản phẩm',
    type: PhysicalInventoryResponseDto,
    required: false,
  })
  @Expose()
  @Type(() => PhysicalInventoryResponseDto)
  inventory?: PhysicalInventoryResponseDto;

  @ApiProperty({
    description: 'Cấu hình vận chuyển',
    example: {
      widthCm: 20,
      heightCm: 5,
      lengthCm: 25,
      weightGram: 150
    },
  })
  @Expose()
  declare shipmentConfig: {
    widthCm: number;
    heightCm: number;
    lengthCm: number;
    weightGram: number;
  };
}
