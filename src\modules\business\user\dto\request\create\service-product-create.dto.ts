import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsEnum,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsNumber,
  IsString,
  ValidateNested,
} from 'class-validator';
import { ProductTypeEnum } from '@modules/business/enums';
import { BaseProductDto } from '../base-product/base-product.dto';
import { ServiceAdvancedInfoDto } from '../../advanced-info';
import { ProductPriceDto } from '../product-price/product-price.type';

/**
 * DTO cho việc tạo sản phẩm dịch vụ (SERVICE)
 * <PERSON><PERSON> thừa từ BaseAdvancedProductDto và thêm các trường đặc thù cho dịch vụ
 */
export class ServiceProductCreateDto extends BaseProductDto {
  @ApiProperty({
    description: 'Thông tin nâng cao cho sản phẩm dịch vụ',
    type: ServiceAdvancedInfoDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => ServiceAdvancedInfoDto)
  advancedInfo: ServiceAdvancedInfoDto;

  // Override price để bắt buộc cho SERVICE
  @ApiProperty({
    description: 'Thông tin giá sản phẩm - Bắt buộc đối với sản phẩm dịch vụ',
    oneOf: [
      { $ref: '#/components/schemas/HasPriceDto' },
      { $ref: '#/components/schemas/StringPriceDto' },
      { $ref: '#/components/schemas/ClassificationPriceDto' },
      { $ref: '#/components/schemas/ClassificationStringPriceDto' }
    ],
  })
  @IsNotEmpty()
  @IsObject()
  declare price: ProductPriceDto; // Required for service products

  // Service-specific fields for frontend compatibility
  @ApiProperty({
    description: 'Thời gian thực hiện dịch vụ (timestamp)',
    example: 1704067200000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  serviceTime?: number;

  @ApiProperty({
    description: 'Thời lượng dịch vụ (phút)',
    example: '60',
    required: false,
  })
  @IsOptional()
  @IsString()
  serviceDuration?: string;

  @ApiProperty({
    description: 'Nhà cung cấp dịch vụ',
    example: 'Công ty tư vấn ABC',
    required: false,
  })
  @IsOptional()
  @IsString()
  serviceProvider?: string;

  @ApiProperty({
    description: 'Loại dịch vụ',
    example: 'CONSULTATION',
    enum: ['CONSULTATION', 'TRAINING', 'SUPPORT', 'MAINTENANCE', 'OTHER'],
    required: false,
  })
  @IsOptional()
  @IsString()
  serviceType?: string;

  @ApiProperty({
    description: 'Địa điểm thực hiện dịch vụ',
    example: 'AT_CENTER',
    enum: ['AT_CENTER', 'AT_CUSTOMER', 'ONLINE', 'HYBRID'],
    required: false,
  })
  @IsOptional()
  @IsString()
  serviceLocation?: string;

  @ApiProperty({
    description: 'Mô tả dịch vụ',
    example: 'Dịch vụ tư vấn chuyên nghiệp',
    required: false,
  })
  @IsOptional()
  @IsString()
  serviceDescription?: string;
}
