import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsObject,
  ValidateNested,
} from 'class-validator';
import { BaseProductDto } from '../base-product/base-product.dto';
import { EventAdvancedInfoDto } from '../../advanced-info';
/**
 * DTO cho việc tạo sản phẩm sự kiện (EVENT)
 * Kế thừa từ BaseEventProductDto và thêm các trường đặc thù cho sự kiện
 */
export class EventProductCreateDto extends BaseProductDto {
  @ApiProperty({
    description: 'Thông tin nâng cao cho sản phẩm sự kiện',
    type: EventAdvancedInfoDto,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => EventAdvancedInfoDto)
  advancedInfo: EventAdvancedInfoDto;
}
