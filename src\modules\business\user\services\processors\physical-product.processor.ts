import { Injectable, Logger } from '@nestjs/common';
import {
  InventoryRepository,
  PhysicalWarehouseRepository,
} from '@modules/business/repositories';
import { PhysicalProductCreateDto } from '../../dto';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { UserProduct } from '@modules/business/entities';
import { ValidationHelper } from '../../helpers/validation.helper';
import { ClassificationService } from '../classification.service';
import { CreateProductProcessor } from './create-product.processor';
import { CreateProductResult } from './create-product-orchestrator';

/**
 * Processor chuyên xử lý logic tạo sản phẩm vật lý
 * Bóc tách từ UserProductService để tối ưu hóa và dễ maintain
 */
@Injectable()
export class PhysicalProductProcessor {
  private readonly logger = new Logger(PhysicalProductProcessor.name);

  constructor(
    private readonly createProcessor: CreateProductProcessor,
    private readonly inventoryRepository: InventoryRepository,
    private readonly physicalWarehouseRepository: PhysicalWarehouseRepository,
    private readonly validationHelper: ValidationHelper,
    private readonly classificationService: ClassificationService,
  ) {}

  /**
   * Tạo sản phẩm vật lý hoàn chỉnh
   */
  async createPhysicalProduct(
    dto: PhysicalProductCreateDto,
    userId: number,
  ): Promise<CreateProductResult> {
    this.logger.log(`Creating PHYSICAL product: ${dto.name}`);

    // BƯỚC 1: Validate dữ liệu đầu vào cho sản phẩm vật lý
    await this.validatePhysicalProductData(dto);

    // BƯỚC 2: Xử lý custom fields và tạo metadata
    const { customFields, metadata } = await this.createProcessor.processCustomFields(dto);

    // BƯỚC 3: Tạo entity sản phẩm cơ bản
    const product = await this.createProcessor.createBaseProduct(dto, userId, metadata);

    // BƯỚC 4: Lưu sản phẩm vào database để có ID
    const savedProduct = await this.createProcessor.saveProduct(product);

    // BƯỚC 5: Xử lý hình ảnh sản phẩm
    const { imageEntries, imagesUploadUrls } = await this.createProcessor.processProductImages(dto, Date.now());

    // BƯỚC 6: Cập nhật sản phẩm với thông tin hình ảnh
    savedProduct.images = imageEntries;
    await this.createProcessor.saveProduct(savedProduct);

    // BƯỚC 7: Xử lý inventory (kho hàng)
    const inventory = await this.processPhysicalInventory(savedProduct.id, dto.inventory, userId);

    // BƯỚC 8: Xử lý classifications (phân loại sản phẩm)
    const classifications = await this.processClassifications(savedProduct.id, dto.classifications || [], userId);

    // BƯỚC 9: Lấy sản phẩm cuối cùng sau khi đã xử lý xong
    const finalProduct = await this.createProcessor.getProductById(savedProduct.id);

    // BƯỚC 10: Tạo object chứa upload URLs cho frontend
    const uploadUrls = imagesUploadUrls.length > 0 ? {
      productId: finalProduct.id.toString(),
      imagesUploadUrls: imagesUploadUrls
    } : null;

    // Trả về kết quả cuối cùng
    return {
      product: finalProduct,
      uploadUrls,
      additionalInfo: {
        inventory,
        classifications
      }
    };
  }

  /**
   * Validate dữ liệu đầu vào cho sản phẩm vật lý
   */
  private async validatePhysicalProductData(dto: PhysicalProductCreateDto): Promise<void> {
    // Kiểm tra inventory có tồn tại không (bắt buộc cho sản phẩm vật lý)
    if (!dto.inventory) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVENTORY_VALIDATION_FAILED,
        'Inventory is required for physical products',
      );
    }

    // Kiểm tra price có tồn tại không (bắt buộc cho sản phẩm vật lý)
    if (!dto.price) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Price is required for physical products',
      );
    }

    // Validate giá sản phẩm theo business rules
    this.validationHelper.validateProductPrice(dto.price, dto.typePrice, dto.productType);

    this.logger.log(`Validated physical product data for: ${dto.name}`);
  }

  /**
   * Xử lý inventory cho sản phẩm vật lý
   */
  private async processPhysicalInventory(
    productId: number,
    inventoryDto: any,
    userId: number,
  ): Promise<any> {
    if (!inventoryDto) {
      return null;
    }

    this.logger.log(`Processing inventory for physical product: ${productId}`);

    // TODO: Implement logic từ service gốc
    // Tạo bản ghi inventory cho sản phẩm vật lý
    // const inventory = await this.createInventoryForPhysicalProduct(productId, inventoryDto, userId);
    
    // Placeholder return
    return {
      productId,
      quantity: inventoryDto.quantity || 0,
      minStock: inventoryDto.minStock || 0,
      maxStock: inventoryDto.maxStock || null,
      // ... other inventory fields
    };
  }

  /**
   * Xử lý classifications cho sản phẩm
   */
  private async processClassifications(
    productId: number,
    classificationsDto: any[],
    userId: number,
  ): Promise<any[]> {
    if (!classificationsDto || classificationsDto.length === 0) {
      return [];
    }

    this.logger.log(`Processing ${classificationsDto.length} classifications for product: ${productId}`);

    // TODO: Implement logic từ service gốc
    // Tạo các bản ghi classification nếu có
    // const classifications = await this.createClassificationsForProduct(productId, classificationsDto, userId);
    
    // Placeholder return
    return classificationsDto.map((classification, index) => ({
      id: index + 1,
      productId,
      name: classification.name || `Classification ${index + 1}`,
      // ... other classification fields
    }));
  }
}
