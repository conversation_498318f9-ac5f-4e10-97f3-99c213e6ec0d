import { Injectable } from '@nestjs/common';
import { UserProduct } from '@modules/business/entities/user-product.entity';
import { BaseProductDto } from '../../../dto/base/base-product.dto';
import { BaseProductResponseDto } from '../../../dto/base/base-product-response.dto';
import { ProductTypeEnum } from '@modules/business/enums';

/**
 * Interface cho Product Creation Context
 */
export interface ProductCreationContext {
  userId: number;
  timestamp: number;
}

/**
 * Interface cho Product Creation Result
 */
export interface ProductCreationResult {
  product: UserProduct;
  imagesUploadUrls: any[];
  advancedImagesUploadUrls?: any[];
  inventory?: any;
  classifications?: any[];
  advancedInfo?: any;
}

/**
 * Abstract Base Handler cho tất cả các loại sản phẩm
 * Định nghĩa interface chung cho việc xử lý sản phẩm
 */
@Injectable()
export abstract class BaseProductHandler<
  TCreateDto extends BaseProductDto,
  TResponseDto extends BaseProductResponseDto
> {
  /**
   * Loại sản phẩm mà handler này xử lý
   */
  abstract readonly productType: ProductTypeEnum;

  /**
   * Validate dữ liệu đầu vào
   */
  abstract validate(dto: TCreateDto, userId: number): Promise<void>;

  /**
   * Tạo base product entity
   */
  abstract createBaseProduct(dto: TCreateDto, userId: number): Promise<UserProduct>;

  /**
   * Xử lý advanced info (nếu có)
   */
  abstract processAdvancedInfo?(
    product: UserProduct,
    dto: TCreateDto,
    context: ProductCreationContext
  ): Promise<any>;

  /**
   * Xử lý inventory (nếu có)
   */
  abstract processInventory?(
    product: UserProduct,
    dto: TCreateDto,
    context: ProductCreationContext
  ): Promise<any>;

  /**
   * Xử lý classifications
   */
  abstract processClassifications(
    productId: number,
    dto: TCreateDto,
    context: ProductCreationContext
  ): Promise<any[]>;

  /**
   * Xử lý images upload URLs
   */
  abstract processImages(dto: TCreateDto): Promise<{
    imageEntries: any[];
    imagesUploadUrls: any[];
  }>;

  /**
   * Xử lý advanced images upload URLs (nếu có)
   */
  abstract processAdvancedImages?(dto: TCreateDto): Promise<{
    advancedImagesUploadUrls: any[];
  }>;

  /**
   * Map entity sang response DTO
   */
  abstract mapToResponseDto(
    product: UserProduct,
    result: ProductCreationResult
  ): Promise<TResponseDto>;

  /**
   * Xử lý custom fields và metadata
   */
  protected async processCustomFields(
    dto: TCreateDto,
    additionalMetadata?: any
  ): Promise<{ customFields: any[]; metadata: any }> {
    let customFields: any[] = [];
    let metadata: any = {};

    // Xử lý custom fields nếu có
    if (dto.customFields && dto.customFields.length > 0) {
      customFields = dto.customFields.map(field => ({
        customFieldId: field.customFieldId,
        value: field.value
      }));
    }

    // Tạo metadata
    metadata = {
      customFields,
      ...additionalMetadata
    };

    return { customFields, metadata };
  }

  /**
   * Xử lý shipment config
   */
  protected getShipmentConfig(dto: TCreateDto): any {
    if (dto.shipmentConfig) {
      return {
        widthCm: dto.shipmentConfig.widthCm || 25,
        heightCm: dto.shipmentConfig.heightCm || 5,
        lengthCm: dto.shipmentConfig.lengthCm || 30,
        weightGram: dto.shipmentConfig.weightGram || 200
      };
    }

    // Default shipment config
    return {
      widthCm: 25,
      heightCm: 5,
      lengthCm: 30,
      weightGram: 200
    };
  }

  /**
   * Validate common fields
   */
  protected validateCommonFields(dto: TCreateDto): void {
    if (!dto.name || dto.name.trim().length === 0) {
      throw new Error('Product name is required');
    }

    // Note: productType validation is handled at the service level
    // since the base DTO doesn't include productType field
  }
}
