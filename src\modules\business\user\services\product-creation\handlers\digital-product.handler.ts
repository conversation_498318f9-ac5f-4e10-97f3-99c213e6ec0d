import { Injectable } from '@nestjs/common';
import { UserProduct } from '@modules/business/entities/user-product.entity';
import { ProductTypeEnum, EntityStatusEnum } from '@modules/business/enums';
import { DigitalProductCreateDto } from '../../../dto/create/digital-product-create.dto';
import { DigitalProductResponseDto } from '../../../dto/response/digital-product-response.dto';
import { BaseProductHandler, ProductCreationContext, ProductCreationResult } from './base-product.handler';
import { DigitalProductMapper } from '../mappers/digital-product.mapper';

/**
 * Handler cho Digital Product
 * Xử lý logic riêng cho sản phẩm số
 */
@Injectable()
export class DigitalProductHandler extends BaseProductHandler<
  DigitalProductCreateDto,
  DigitalProductResponseDto
> {
  readonly productType = ProductTypeEnum.DIGITAL;

  constructor(
    private readonly digitalProductMapper: DigitalProductMapper,
    // Inject các services cần thiết
    // private readonly advancedInfoService: AdvancedInfoService,
    // private readonly classificationService: ClassificationService,
    // private readonly imageService: ImageService,
  ) {
    super();
  }

  /**
   * Validate dữ liệu đầu vào cho digital product
   */
  async validate(dto: DigitalProductCreateDto, userId: number): Promise<void> {
    // Validate common fields
    this.validateCommonFields(dto);

    // Validate digital-specific fields
    if (!dto.advancedInfo) {
      throw new Error('Advanced info is required for digital products');
    }

    if (!dto.price) {
      throw new Error('Price is required for digital products');
    }

    // Validate advanced info structure
    if (dto.advancedInfo.purchaseCount !== undefined && dto.advancedInfo.purchaseCount < 0) {
      throw new Error('Purchase count cannot be negative');
    }

    // Validate variants if present
    if (dto.advancedInfo.variantMetadata?.variants && dto.advancedInfo.variantMetadata.variants.length > 0) {
      for (const variant of dto.advancedInfo.variantMetadata.variants) {
        if (!variant.name || variant.name.trim().length === 0) {
          throw new Error('Variant name is required');
        }

        if (!variant.sku || variant.sku.trim().length === 0) {
          throw new Error('Variant SKU is required');
        }

        if (variant.availableQuantity !== undefined && variant.availableQuantity < 0) {
          throw new Error('Variant available quantity cannot be negative');
        }

        if (variant.minQuantityPerPurchase !== undefined && variant.minQuantityPerPurchase < 0) {
          throw new Error('Variant minimum quantity per purchase cannot be negative');
        }

        if (variant.maxQuantityPerPurchase !== undefined && variant.maxQuantityPerPurchase < 0) {
          throw new Error('Variant maximum quantity per purchase cannot be negative');
        }

        if (variant.minQuantityPerPurchase !== undefined && 
            variant.maxQuantityPerPurchase !== undefined && 
            variant.minQuantityPerPurchase > variant.maxQuantityPerPurchase) {
          throw new Error('Variant minimum quantity cannot be greater than maximum quantity');
        }
      }
    }
  }

  /**
   * Tạo base product entity cho digital product
   */
  async createBaseProduct(dto: DigitalProductCreateDto, userId: number): Promise<UserProduct> {
    // Xử lý variant metadata
    const variantMetadata = await this.processVariantMetadata(dto);
    const { customFields, metadata } = await this.processCustomFields(dto, variantMetadata);

    const product = new UserProduct();
    product.name = dto.name;
    product.productType = ProductTypeEnum.DIGITAL;
    product.price = dto.price;
    product.typePrice = dto.typePrice;
    product.description = dto.description || '';
    product.images = []; // Sẽ được cập nhật sau khi xử lý images
    product.tags = dto.tags || [];
    product.metadata = metadata;
    product.createdBy = userId;
    product.createdAt = Date.now();
    product.updatedAt = Date.now();
    product.status = EntityStatusEnum.PENDING;

    // Set embedding fields to null
    product.nameEmbedding = null;
    product.descriptionEmbedding = null;
    product.tagsEmbedding = null;

    // Digital products have zero shipment config
    product.shipmentConfig = {
      widthCm: 0,
      heightCm: 0,
      lengthCm: 0,
      weightGram: 0
    };

    return product;
  }

  /**
   * Xử lý advanced info cho digital product
   */
  async processAdvancedInfo(
    product: UserProduct,
    dto: DigitalProductCreateDto,
    context: ProductCreationContext
  ): Promise<any> {
    // Logic tạo advanced info cho digital product
    const advancedInfo = {
      productId: product.id,
      productType: ProductTypeEnum.DIGITAL,
      purchaseCount: dto.advancedInfo.purchaseCount || 0,
      variants: dto.advancedInfo.variantMetadata?.variants || [],
      images: [], // Sẽ được cập nhật sau khi xử lý advanced images
      createdAt: context.timestamp,
      updatedAt: context.timestamp
    };

    // Logic lưu vào database
    // const savedAdvancedInfo = await this.advancedInfoService.create(advancedInfo);
    
    return advancedInfo;
  }

  /**
   * Xử lý inventory cho digital product (không cần thiết cho digital)
   */
  async processInventory(
    product: UserProduct,
    dto: DigitalProductCreateDto,
    context: ProductCreationContext
  ): Promise<any> {
    // Digital products don't have traditional inventory
    // Inventory is managed through variants if present
    return null;
  }

  /**
   * Xử lý classifications cho digital product
   */
  async processClassifications(
    productId: number,
    dto: DigitalProductCreateDto,
    context: ProductCreationContext
  ): Promise<any[]> {
    if (!dto.classifications || dto.classifications.length === 0) {
      return [];
    }

    // Logic tạo classifications
    const classifications = [];
    for (const classificationDto of dto.classifications) {
      // const classification = await this.classificationService.create(productId, classificationDto, context.userId);
      // classifications.push(classification);
    }

    return classifications;
  }

  /**
   * Xử lý images upload URLs
   */
  async processImages(dto: DigitalProductCreateDto): Promise<{
    imageEntries: any[];
    imagesUploadUrls: any[];
  }> {
    const imageEntries: any[] = [];
    const imagesUploadUrls: any[] = [];
    const now = Date.now();

    if (dto.imagesMediaTypes && dto.imagesMediaTypes.length > 0) {
      for (let i = 0; i < dto.imagesMediaTypes.length; i++) {
        const mediaType = dto.imagesMediaTypes[i];
        const fileName = `digital-product-image-${i}-${now}`;

        // Logic tạo presigned URL
        const key = `${fileName}.${this.getFileExtension(mediaType)}`;
        const url = `https://presigned-url-example.com/${key}`;

        imageEntries.push({
          key: key,
          position: i
        });

        imagesUploadUrls.push({
          url: url,
          key: key,
          index: i
        });
      }
    }

    return { imageEntries, imagesUploadUrls };
  }

  /**
   * Xử lý advanced images upload URLs
   */
  async processAdvancedImages(dto: DigitalProductCreateDto): Promise<{
    advancedImagesUploadUrls: any[];
  }> {
    const advancedImagesUploadUrls: any[] = [];
    const now = Date.now();

    // Xử lý variant images
    if (dto.advancedInfo.variantMetadata?.variants && dto.advancedInfo.variantMetadata.variants.length > 0) {
      for (let variantIndex = 0; variantIndex < dto.advancedInfo.variantMetadata.variants.length; variantIndex++) {
        const variant = dto.advancedInfo.variantMetadata.variants[variantIndex];
        
        if (variant.imagesMediaTypes && variant.imagesMediaTypes.length > 0) {
          for (let imageIndex = 0; imageIndex < variant.imagesMediaTypes.length; imageIndex++) {
            const mediaType = variant.imagesMediaTypes[imageIndex];
            const fileName = `digital-variant-${variantIndex}-image-${imageIndex}-${now}`;

            const key = `${fileName}.${this.getFileExtension(mediaType)}`;
            const url = `https://presigned-url-example.com/${key}`;

            advancedImagesUploadUrls.push({
              url: url,
              key: key,
              type: 'variant',
              index: imageIndex,
              position: variantIndex
            });
          }
        }
      }
    }

    return { advancedImagesUploadUrls };
  }

  /**
   * Map entity sang response DTO
   */
  async mapToResponseDto(
    product: UserProduct,
    result: ProductCreationResult
  ): Promise<DigitalProductResponseDto> {
    return this.digitalProductMapper.mapToResponseDto(product, result);
  }

  /**
   * Private helper methods
   */
  private async processVariantMetadata(dto: DigitalProductCreateDto): Promise<any> {
    if (!dto.advancedInfo.variantMetadata?.variants || dto.advancedInfo.variantMetadata.variants.length === 0) {
      return {};
    }

    const processedVariants = dto.advancedInfo.variantMetadata.variants.map(variant => {
      const { imagesMediaTypes, ...processedVariant } = variant;

      // Remove images field if it exists (optional field)
      if ('images' in processedVariant) {
        delete (processedVariant as any).images;
      }

      return processedVariant;
    });

    return {
      variants: processedVariants
    };
  }

  private getFileExtension(mediaType: string): string {
    const extensions: { [key: string]: string } = {
      'image/jpeg': 'jpg',
      'image/jpg': 'jpg',
      'image/png': 'png',
      'image/gif': 'gif',
      'image/webp': 'webp'
    };
    return extensions[mediaType] || 'jpg';
  }
}
