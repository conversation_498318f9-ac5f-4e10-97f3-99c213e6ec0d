/**
 * Export tất cả các processor cho product operations
 */

// Update Product Processors
export { UpdateProductProcessor } from './update-product.processor';
export { UpdateProductOrchestrator, UpdateProductResult, UpdateProductContext, UpdateStep, UpdateProgress } from './update-product-orchestrator';

// Create Product Processors
export { CreateProductOrchestrator, CreateProductResult, CreateProductContext, CreateStep, CreateProgress } from './create-product-orchestrator';
export { CreateProductProcessor } from './create-product.processor';
export { PhysicalProductProcessor } from './physical-product.processor';
export { DigitalProductProcessor } from './digital-product.processor';
export { EventProductProcessor } from './event-product.processor';
export { ServiceProductProcessor } from './service-product.processor';
export { ComboProductProcessor } from './combo-product.processor';
