// Import các thư viện cần thiết từ NestJS
import { Injectable, Logger } from '@nestjs/common';

// Import các repository để tương tác với database
import {
  UserProductRepository,
  CustomFieldRepository,
  InventoryRepository,
  PhysicalWarehouseRepository,
  ProductAdvancedInfoRepository,
} from '@modules/business/repositories';

// Import các DTO cho business logic
import {
  BusinessCreateProductDto,
  BusinessUpdateProductDto,
  BusinessProductResponseDto as ProductResponseDto,
  QueryProductDto,
  ClassificationResponseDto,
  CreateClassificationDto,
  BulkDeleteProductDto,
  BulkDeleteProductResponseDto,
  ProductInventoryDto,
  WarehouseListDto,
} from '../dto';

// Import DTO cho inventory
import {
  InventoryResponseDto,
  QueryInventoryDto,
} from '../dto/inventory';

// Import các enum định nghĩa trạng thái và loại sản phẩm
import { EntityStatusEnum, ProductTypeEnum, PriceTypeEnum } from '@modules/business/enums';

// Import exception handling
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';

// Import các entity
import { UserProduct, Inventory, ProductAdvancedInfo } from '@modules/business/entities';

// Import decorator cho transaction
import { Transactional } from 'typeorm-transactional';

// Import utility cho data transformation
import { plainToInstance } from 'class-transformer';

// Import response wrapper
import { PaginatedResult } from '@common/response';

// Import các helper class
import { UserProductHelper } from '../helpers/user-product.helper';
import { MetadataHelper } from '../helpers/metadata.helper';
import { ValidationHelper } from '../helpers/validation.helper';
import { ProductValidationHelper } from '../helpers/product-validation.helper';

// Import các service cho S3 và CDN
import { S3Service } from '@shared/services/s3.service';
import { CdnService } from '@shared/services/cdn.service';

// Import utility cho S3 key generation
import { CategoryFolderEnum, generateS3Key } from '@shared/utils/generators/s3-key-generator.util';
import { FileSizeEnum, ImageTypeEnum, TimeIntervalEnum } from '@shared/utils';

// Import service cho classification
import { ClassificationService } from './classification.service';

// Import TypeORM DataSource
import { DataSource } from 'typeorm';

// Import processors
import { CreateProductOrchestrator } from './processors';

// Import các DTO mới
import { CreatedProductDto } from '../dto/base/created-product.dto';



/**
 * Service xử lý logic nghiệp vụ cho sản phẩm của người dùng
 * Chịu trách nhiệm:
 * - Tạo, cập nhật, xóa sản phẩm
 * - Xử lý các loại sản phẩm khác nhau (Physical, Digital, Event, Service, Combo)
 * - Quản lý inventory, classifications, custom fields
 * - Xử lý upload hình ảnh lên S3
 * - Validate dữ liệu đầu vào
 */
@Injectable()
export class UserProductService {
  // Logger để ghi log các hoạt động của service
  private readonly logger = new Logger(UserProductService.name);

  constructor(
    // Repository để tương tác với bảng user_products
    private readonly userProductRepository: UserProductRepository,
    // Repository để tương tác với bảng custom_fields
    private readonly customFieldRepository: CustomFieldRepository,
    // Repository để tương tác với bảng inventories
    private readonly inventoryRepository: InventoryRepository,
    // Repository để tương tác với bảng physical_warehouses
    private readonly physicalWarehouseRepository: PhysicalWarehouseRepository,
    // Repository để tương tác với bảng product_advanced_info
    private readonly productAdvancedInfoRepository: ProductAdvancedInfoRepository,
    // Helper để xử lý logic liên quan đến UserProduct
    private readonly userProductHelper: UserProductHelper,
    // Helper để xử lý metadata và custom fields
    private readonly metadataHelper: MetadataHelper,
    // Service để tương tác với Amazon S3
    private readonly s3Service: S3Service,
    // Service để tạo CDN URLs
    private readonly cdnService: CdnService,
    // Service để xử lý classifications
    private readonly classificationService: ClassificationService,
    // Helper để validate dữ liệu
    private readonly validationHelper: ValidationHelper,
    // Helper để validate thông tin sản phẩm
    private readonly productValidationHelper: ProductValidationHelper,
    // DataSource để thực hiện raw queries
    private readonly dataSource: DataSource,
    // Orchestrator để xử lý tạo sản phẩm
    private readonly createProductOrchestrator: CreateProductOrchestrator,
  ) {}

  /**
   * Tạo sản phẩm mới với type-safe handling
   * Hỗ trợ tất cả các loại sản phẩm: Physical, Digital, Event, Service, Combo
   * @param createProductDto DTO chứa thông tin sản phẩm mới
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm đã tạo kèm theo upload URLs và thông tin bổ sung
   */
  @Transactional()
  async createProduct(
    createProductDto: CreatedProductDto,
    userId: number,
  ): Promise<ProductResponseDto> {
    // Delegate việc tạo sản phẩm cho CreateProductOrchestrator
    return await this.createProductOrchestrator.createProduct(createProductDto, userId);
  }



  // NOTE: createPhysicalProductEntity method đã được bóc tách vào PhysicalProductProcessor

  // NOTE: createDigitalProductEntity method đã được bóc tách vào DigitalProductProcessor

  // NOTE: createEventProductEntity method đã được bóc tách vào EventProductProcessor

  /**
   * Private method xử lý tạo Service Product Entity
   * Luồng xử lý:
   * 1. Validate dữ liệu đầu vào (advancedInfo, price)
   * 2. Xử lý service metadata và custom fields
   * 3. Tạo sản phẩm với shipment config = 0
   * 4. Xử lý hình ảnh sản phẩm và service packages
   * 5. Tạo advanced info (service packages)
   * 6. Xử lý classifications
   * 7. Trả về kết quả
   */
  private async createServiceProductEntity(
    dto: ServiceProductCreateDto,
    userId: number,
  ): Promise<{ product: UserProduct; uploadUrls: any; advancedInfo: any; classifications: any }> {
    this.logger.log(`Creating SERVICE product: ${dto.name}`);

    // BƯỚC 1: Validate dữ liệu đầu vào cho sản phẩm dịch vụ
    // Kiểm tra advancedInfo có tồn tại không (bắt buộc cho sản phẩm dịch vụ - chứa service packages)
    if (!dto.advancedInfo) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Advanced info is required for service products',
      );
    }

    // Kiểm tra price có tồn tại không (bắt buộc cho sản phẩm dịch vụ)
    if (!dto.price) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Price is required for service products',
      );
    }

    // Validate giá sản phẩm theo business rules
    this.validationHelper.validateProductPrice(dto.price, dto.typePrice, dto.productType);

    // Bỏ qua validation advanced info cho cấu trúc DTO mới
    // this.productValidationHelper.validateAdvancedInfo(dto.productType, dto.advancedInfo);

    // BƯỚC 2: Xử lý service metadata và custom fields
    // Tạo metadata đặc biệt cho dịch vụ
    const serviceMetadata = this.processServiceMetadata(dto);
    // Xử lý custom fields kết hợp với service metadata
    const { customFields, metadata } = await this.processCustomFields(dto, serviceMetadata);

    // BƯỚC 3: Tạo sản phẩm cơ bản với shipment config = 0
    // Sản phẩm dịch vụ không cần vận chuyển
    const product = await this.createBaseProduct(dto, userId, metadata);
    product.shipmentConfig = { widthCm: 0, heightCm: 0, lengthCm: 0, weightGram: 0 };

    // Lưu sản phẩm để có ID
    const savedProduct = await this.userProductRepository.save(product);

    // BƯỚC 4: Xử lý hình ảnh sản phẩm chính
    const { imageEntries, imagesUploadUrls } = await this.processProductImages(dto, Date.now());

    // Cập nhật sản phẩm với thông tin hình ảnh
    savedProduct.images = imageEntries;
    await this.userProductRepository.save(savedProduct);

    // BƯỚC 5: Tạo advanced info (service packages)
    // Tạo bản ghi trong bảng product_advanced_info chứa thông tin service packages
    const advancedInfo = await this.createAdvancedInfo(savedProduct.id, dto.productType, dto.advancedInfo, []);

    // Xử lý hình ảnh cho service packages
    const advancedImagesUploadUrls = await this.createAdvancedImagesUploadUrls(dto.advancedInfo, dto.productType, Date.now());

    // Cập nhật detail_id để liên kết với advanced info
    if (advancedInfo?.id) {
      savedProduct.detail_id = advancedInfo.id;
      await this.userProductRepository.save(savedProduct);

      // Cập nhật advanced info với image keys nếu có hình ảnh service packages
      if (advancedImagesUploadUrls.length > 0) {
        await this.updateAdvancedInfoWithImageKeys(advancedInfo.id, dto.productType, advancedImagesUploadUrls);
      }
    }

    // BƯỚC 6: Xử lý classifications
    const classifications = await this.processClassifications(savedProduct.id, dto.classifications || [], userId);

    // BƯỚC 7: Lấy sản phẩm cuối cùng SAU KHI đã cập nhật advanced info với image keys
    const finalProduct = await this.userProductRepository.findById(savedProduct.id);
    if (!finalProduct) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
        `Cannot retrieve product after creation: ${savedProduct.id}`,
      );
    }

    // Tạo object chứa upload URLs cho cả sản phẩm chính và service packages
    const uploadUrls: any = {
      productId: finalProduct.id.toString(),
      imagesUploadUrls: imagesUploadUrls || []
    };

    // Thêm upload URLs cho hình ảnh service packages nếu có
    if (advancedImagesUploadUrls && advancedImagesUploadUrls.length > 0) {
      uploadUrls.advancedImagesUploadUrls = advancedImagesUploadUrls;
    }

    return {
      product: finalProduct,
      uploadUrls: (imagesUploadUrls.length > 0 || advancedImagesUploadUrls.length > 0) ? uploadUrls : null,
      advancedInfo,
      classifications
    };
  }

  /**
   * Private method xử lý tạo Combo Product Entity
   * Luồng xử lý:
   * 1. Validate dữ liệu đầu vào (advancedInfo, price, combo products)
   * 2. Xử lý custom fields
   * 3. Tạo sản phẩm với shipment config tính toán từ sản phẩm con
   * 4. Xử lý hình ảnh sản phẩm
   * 5. Tạo advanced info (combo products)
   * 6. Xử lý classifications
   * 7. Trả về kết quả
   */
  private async createComboProductEntity(
    dto: ComboProductCreateDto,
    userId: number,
  ): Promise<{ product: UserProduct; uploadUrls: any; advancedInfo: any; classifications: any }> {
    this.logger.log(`Creating COMBO product: ${dto.name}`);

    // BƯỚC 1: Validate dữ liệu đầu vào cho sản phẩm combo
    // Kiểm tra advancedInfo có tồn tại không (bắt buộc cho sản phẩm combo - chứa danh sách sản phẩm con)
    if (!dto.advancedInfo) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Advanced info is required for combo products',
      );
    }

    // Kiểm tra price có tồn tại không (bắt buộc cho sản phẩm combo)
    if (!dto.price) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Price is required for combo products',
      );
    }

    // Validate giá sản phẩm theo business rules
    this.validationHelper.validateProductPrice(dto.price, dto.typePrice, dto.productType);

    // Validate thông tin advanced info và các sản phẩm trong combo
    this.productValidationHelper.validateAdvancedInfo(dto.productType, dto.advancedInfo);
    // Kiểm tra các sản phẩm con trong combo có tồn tại và thuộc về user không
    await this.validationHelper.validateComboProducts(dto.advancedInfo as any, userId);

    // BƯỚC 2: Xử lý custom fields
    const { customFields, metadata } = await this.processCustomFields(dto);

    // BƯỚC 3: Tạo sản phẩm cơ bản với shipment config tính toán từ sản phẩm con
    const product = await this.createBaseProduct(dto, userId, metadata);
    // Shipment config sẽ được tính toán từ các sản phẩm con hoặc sử dụng default
    product.shipmentConfig = dto.shipmentConfig || { widthCm: 30, heightCm: 10, lengthCm: 40, weightGram: 500 };

    // Lưu sản phẩm để có ID
    const savedProduct = await this.userProductRepository.save(product);

    // BƯỚC 4: Xử lý hình ảnh sản phẩm chính
    const { imageEntries, imagesUploadUrls } = await this.processProductImages(dto, Date.now());

    // Cập nhật sản phẩm với thông tin hình ảnh
    savedProduct.images = imageEntries;
    await this.userProductRepository.save(savedProduct);

    // BƯỚC 5: Tạo advanced info (combo products)
    // Tạo bản ghi trong bảng product_advanced_info chứa thông tin các sản phẩm con
    const advancedInfo = await this.createAdvancedInfo(savedProduct.id, dto.productType, dto.advancedInfo, []);

    // Cập nhật detail_id để liên kết với advanced info
    if (advancedInfo?.id) {
      savedProduct.detail_id = advancedInfo.id;
      await this.userProductRepository.save(savedProduct);
    }

    // BƯỚC 6: Xử lý classifications
    const classifications = await this.processClassifications(savedProduct.id, dto.classifications || [], userId);

    // BƯỚC 7: Lấy sản phẩm cuối cùng
    const finalProduct = await this.userProductRepository.findById(savedProduct.id);
    if (!finalProduct) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
        `Cannot retrieve product after creation: ${savedProduct.id}`,
      );
    }

    // Tạo object chứa upload URLs cho sản phẩm chính
    // Sản phẩm combo chỉ có hình ảnh chính, không có advanced images
    const uploadUrls: any = {
      productId: finalProduct.id.toString(),
      imagesUploadUrls: imagesUploadUrls || []
    };

    return {
      product: finalProduct,
      uploadUrls: imagesUploadUrls.length > 0 ? uploadUrls : null,
      advancedInfo,
      classifications
    };
  }

  /**
   * Helper method: Xử lý custom fields
   * Chức năng:
   * - Lấy thông tin custom fields từ database
   * - Validate custom fields input
   * - Tạo metadata object
   */
  private async processCustomFields(dto: CreatedProductDto, additionalMetadata?: any): Promise<{ customFields: any[]; metadata: any }> {
    let customFields: any[] = [];

    // Kiểm tra có custom fields không
    if (dto.customFields && dto.customFields.length > 0) {
      this.logger.log(`Processing ${dto.customFields.length} custom fields for product`);

      // Lấy danh sách ID của custom fields
      const customFieldIds = this.metadataHelper.extractCustomFieldIds(dto.customFields);

      // Lấy thông tin chi tiết custom fields từ database
      customFields = await this.customFieldRepository.findByIds(customFieldIds);

      // Validate custom fields input với database
      this.metadataHelper.validateCustomFieldInputs(dto.customFields, customFields);
    }

    // Tạo metadata object kết hợp custom fields và additional metadata
    const metadata = this.metadataHelper.buildMetadata(
      dto.customFields,
      customFields,
      additionalMetadata
    );

    return { customFields, metadata };
  }

  /**
   * Helper method: Tạo entity sản phẩm cơ bản
   * Chức năng:
   * - Tạo UserProduct entity với thông tin cơ bản
   * - Set các giá trị mặc định
   * - Xử lý embedding fields
   * - Set shipment config mặc định
   */
  private async createBaseProduct(dto: CreatedProductDto, userId: number, metadata: any): Promise<UserProduct> {
    const product = new UserProduct();

    // Thông tin cơ bản của sản phẩm
    product.name = dto.name;
    product.productType = dto.productType;
    product.price = dto.price || null; // Xử lý trường hợp undefined price cho EVENT products
    product.typePrice = dto.typePrice;
    product.description = dto.description || '';
    product.images = []; // Sẽ được cập nhật sau khi xử lý hình ảnh
    product.tags = dto.tags || [];
    product.metadata = metadata;

    // Thông tin audit
    product.createdBy = userId;
    product.createdAt = Date.now();
    product.updatedAt = Date.now();
    product.status = EntityStatusEnum.PENDING;

    // Set embedding fields về null (sẽ được tính toán sau)
    product.nameEmbedding = null;
    product.descriptionEmbedding = null;
    product.tagsEmbedding = null;

    // Shipment config mặc định (có thể được override bởi từng loại sản phẩm)
    product.shipmentConfig = dto.shipmentConfig || {
      widthCm: 25,
      heightCm: 5,
      lengthCm: 30,
      weightGram: 200,
    };

    return product;
  }

  /**
   * Helper method: Xử lý hình ảnh sản phẩm
   * Chức năng:
   * - Tạo presigned URLs cho upload hình ảnh lên S3
   * - Tạo image entries với key và position
   * - Trả về cả image entries và upload URLs
   */
  private async processProductImages(dto: CreatedProductDto, timestamp: number): Promise<{
    imageEntries: Array<{ key: string; position: number }>;
    imagesUploadUrls: Array<{ url: string; key: string; index: number }>;
  }> {
    const imageEntries: Array<{ key: string; position: number }> = [];
    const imagesUploadUrls: Array<{ url: string; key: string; index: number }> = [];

    // Kiểm tra có hình ảnh cần upload không
    if (dto.imagesMediaTypes && dto.imagesMediaTypes.length > 0) {
      // Xử lý từng hình ảnh
      for (let i = 0; i < dto.imagesMediaTypes.length; i++) {
        try {
          const mediaType = dto.imagesMediaTypes[i] as ImageTypeEnum;
          const fileName = `product-image-${i}-${timestamp}`;

          // Tạo presigned URL cho upload hình ảnh lên S3
          const imageUploadUrl = await this.createImageUploadUrl(fileName, mediaType);

          const key = imageUploadUrl.key;
          const url = imageUploadUrl.url;

          this.logger.debug(`Created presigned URL for image upload: ${key} with position ${i}`);

          // Thêm vào danh sách image entries (lưu trong database)
          imageEntries.push({ key: key, position: i });
          // Thêm vào danh sách upload URLs (trả về frontend)
          imagesUploadUrls.push({ url: url, key: key, index: i });
        } catch (error) {
          this.logger.error(`Failed to create image upload URL at index ${i}: ${error.message}`, error.stack);
        }
      }
    }

    return { imageEntries, imagesUploadUrls };
  }

  /**
   * Helper method: Xử lý inventory cho sản phẩm vật lý
   * Chức năng:
   * - Kiểm tra sử dụng inventory có sẵn hoặc tạo mới
   * - Xử lý logic nghiệp vụ cho inventory
   * - Trả về InventoryResponseDto
   */
  private async processPhysicalInventory(productId: number, inventoryDto: any, userId: number): Promise<InventoryResponseDto> {
    this.logger.log(`Processing inventory for product ${productId} with data: ${JSON.stringify(inventoryDto)}`);

    try {
      if (inventoryDto.inventoryId) {
        // Sử dụng inventory có sẵn
        return await this.useExistingInventory(productId, inventoryDto.inventoryId, userId);
      } else {
        // Tạo inventory mới
        return await this.createOrUpdateProductInventory(productId, inventoryDto, userId);
      }
    } catch (error) {
      this.logger.error(`Error processing inventory for product ${productId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Helper method: Xử lý classifications (phân loại sản phẩm)
   * Chức năng:
   * - Tạo các classification cho sản phẩm
   * - Xử lý song song để tăng hiệu suất
   * - Trả về danh sách ClassificationResponseDto
   */
  private async processClassifications(productId: number, classificationsDto: any[], userId: number): Promise<ClassificationResponseDto[]> {
    // Kiểm tra có classifications không
    if (!classificationsDto || classificationsDto.length === 0) {
      return [];
    }

    try {
      // Tạo tất cả classifications song song
      return await Promise.all(
        classificationsDto.map(classificationDto =>
          this.classificationService.create(productId, classificationDto, userId)
        )
      );
    } catch (error) {
      // Log warning nhưng không throw error để không làm fail việc tạo sản phẩm
      this.logger.warn(`Cannot create classifications for product ${productId}: ${error.message}`);
      return [];
    }
  }

  /**
   * Helper method: Xử lý metadata cho variants của sản phẩm số
   * Chức năng:
   * - Xử lý custom fields cho từng variant
   * - Loại bỏ imagesMediaTypes và images khỏi metadata
   * - Validate và build metadata cho variant custom fields
   * - Trả về processed variants
   */
  private async processDigitalVariantMetadata(dto: DigitalProductCreateDto): Promise<any> {
    const digitalAdvancedInfo = dto.advancedInfo as any;

    // Kiểm tra có variants không
    if (!digitalAdvancedInfo?.variantMetadata?.variants) {
      return {};
    }

    this.logger.log(`Processing variant metadata for digital product with ${digitalAdvancedInfo.variantMetadata.variants.length} variants`);

    // Xử lý từng variant: loại bỏ imagesMediaTypes và xử lý custom fields
    const processedVariants = await Promise.all(
      digitalAdvancedInfo.variantMetadata.variants.map(async (variant: any) => {
        const processedVariant = { ...variant };

        // Xử lý custom fields cho variant nếu có
        if (variant.customFields && variant.customFields.length > 0) {
          this.logger.log(`Processing ${variant.customFields.length} custom fields for variant ${variant.name}`);

          // Lấy danh sách ID của custom fields
          const variantCustomFieldIds = this.metadataHelper.extractCustomFieldIds(variant.customFields);

          // Lấy thông tin chi tiết custom fields từ database
          const variantCustomFields = await this.customFieldRepository.findByIds(variantCustomFieldIds);

          // Validate custom fields input
          this.metadataHelper.validateCustomFieldInputs(variant.customFields, variantCustomFields);

          // Tạo metadata cho variant custom fields
          const variantMetadata = this.metadataHelper.buildMetadataCustomFields(
            variant.customFields,
            variantCustomFields
          );

          processedVariant.customFields = variantMetadata;
        }

        // Loại bỏ imagesMediaTypes và images khỏi metadata (chỉ dùng để tạo upload URLs)
        delete processedVariant.imagesMediaTypes;
        delete processedVariant.images;

        return processedVariant;
      })
    );

    return { variants: processedVariants };
  }

  /**
   * Helper method: Xử lý metadata cho sản phẩm dịch vụ
   * Chức năng:
   * - Thu thập các thông tin đặc biệt của dịch vụ
   * - Tạo service metadata object
   * - Log thông tin để debug
   */
  private processServiceMetadata(dto: ServiceProductCreateDto): any {
    const serviceMetadata: any = {};

    // Thu thập thông tin thời gian dịch vụ
    if (dto.serviceTime !== undefined) {
      serviceMetadata.serviceTime = dto.serviceTime;
    }

    // Thu thập thông tin thời lượng dịch vụ
    if (dto.serviceDuration !== undefined) {
      serviceMetadata.serviceDuration = dto.serviceDuration;
    }

    // Thu thập thông tin nhà cung cấp dịch vụ
    if (dto.serviceProvider !== undefined) {
      serviceMetadata.serviceProvider = dto.serviceProvider;
    }

    // Thu thập thông tin loại dịch vụ
    if (dto.serviceType !== undefined) {
      serviceMetadata.serviceType = dto.serviceType;
    }

    // Thu thập thông tin địa điểm dịch vụ
    if (dto.serviceLocation !== undefined) {
      serviceMetadata.serviceLocation = dto.serviceLocation;
    }

    this.logger.log(`Processing service metadata: ${JSON.stringify(serviceMetadata)}`);
    return serviceMetadata;
  }



  /**
   * Lấy danh sách sản phẩm với phân trang và filter
   * Chức năng:
   * - Lấy danh sách sản phẩm từ repository với các filter
   * - Chuyển đổi entity sang DTO response
   * - Trả về kết quả với pagination metadata
   * @param queryDto DTO chứa các tham số truy vấn (filter, sort, pagination)
   * @returns Danh sách sản phẩm với phân trang
   */
  async getProducts(
    queryDto: QueryProductDto,
  ): Promise<PaginatedResult<ProductResponseDto>> {
    try {
      // Lấy danh sách sản phẩm từ repository với filter và pagination
      const productsResult =
        await this.userProductRepository.findProducts(queryDto);

      // Chuyển đổi từ entity sang DTO response song song để tăng hiệu suất
      const items = await Promise.all(
        productsResult.items.map((product) =>
          this.userProductHelper.mapToProductResponseDto(product),
        ),
      );

      // Trả về kết quả với pagination metadata
      return {
        items,
        meta: productsResult.meta,
      };
    } catch (error) {
      // Nếu là AppException, ném lại để giữ nguyên error code
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác, ghi log chi tiết và wrap thành AppException
      this.logger.error(
        `Lỗi khi lấy danh sách sản phẩm: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_FIND_FAILED,
        `Lỗi khi lấy danh sách sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết sản phẩm theo ID với đầy đủ thông tin liên quan
   * Chức năng:
   * - Lấy thông tin sản phẩm cơ bản
   * - Lấy classifications (phân loại)
   * - Lấy inventory (chỉ cho sản phẩm PHYSICAL)
   * - Lấy thông tin warehouse (nếu có)
   * @param id ID của sản phẩm
   * @returns Chi tiết sản phẩm với đầy đủ thông tin
   */
  async getProductDetail(id: number): Promise<ProductResponseDto> {
    try {
      // Tìm sản phẩm theo ID
      const product = await this.userProductRepository.findById(id);

      // Kiểm tra sản phẩm tồn tại
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm với ID ${id}`,
        );
      }

      // Chuyển đổi từ entity sang DTO response
      const productDto = await this.userProductHelper.mapToProductResponseDto(product);

      // Lấy danh sách phân loại sản phẩm nếu có
      try {
        const classifications = await this.classificationService.getByProductId(id);
        if (classifications && classifications.length > 0) {
          // Thêm danh sách phân loại vào DTO
          productDto['classifications'] = classifications;
        }
      } catch (classificationError) {
        // Ghi log warning nhưng không dừng việc trả về sản phẩm
        this.logger.warn(`Không thể lấy danh sách phân loại cho sản phẩm ${id}: ${classificationError.message}`);
      }

      // Lấy thông tin tồn kho chỉ cho sản phẩm PHYSICAL
      // Các loại sản phẩm khác (DIGITAL, SERVICE, EVENT, COMBO) không có inventory
      if (product.productType === ProductTypeEnum.PHYSICAL) {
        try {
          // Lấy inventory của sản phẩm (có thể có nhiều inventory cho các kho khác nhau)
          const inventoriesResult = await this.inventoryRepository.findAll({ productId: id, limit: 1 });
          if (inventoriesResult.items && inventoriesResult.items.length > 0) {
            // Lấy inventory đầu tiên
            const inventory = inventoriesResult.items[0];

            // Chuyển đổi sang DTO response
            const inventoryDto = plainToInstance(InventoryResponseDto, inventory, {
              excludeExtraneousValues: true,
            });

            // Lấy thông tin warehouse nếu có
            if (inventory.warehouseId) {
              try {
                const warehouse = await this.physicalWarehouseRepository.findByWarehouseIdWithDetails(inventory.warehouseId);
                if (warehouse) {
                  // Thêm thông tin warehouse vào inventory DTO
                  inventoryDto.warehouse = {
                    id: warehouse.id,
                    warehouseId: warehouse.warehouseId,
                    name: warehouse.name,
                    description: warehouse.description,
                    type: warehouse.type,
                    address: warehouse.address,
                    capacity: warehouse.capacity,
                  };
                }
              } catch (warehouseError) {
                this.logger.warn(`Không thể lấy thông tin warehouse cho inventory ${inventory.id}: ${warehouseError.message}`);
              }
            }

            // Thêm inventory vào response
            productDto['inventory'] = inventoryDto;
          }
        } catch (inventoryError) {
          // Ghi log warning nhưng không dừng việc trả về sản phẩm
          this.logger.warn(`Không thể lấy thông tin tồn kho cho sản phẩm ${id}: ${inventoryError.message}`);
        }
      }

      return productDto;
    } catch (error) {
      // Nếu là AppException, ném lại để giữ nguyên error code
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác, ghi log chi tiết và wrap thành AppException
      this.logger.error(
        `Lỗi khi lấy chi tiết sản phẩm: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_FIND_FAILED,
        `Lỗi khi lấy chi tiết sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Tạo nhiều sản phẩm cùng lúc
   * @param batchCreateDto DTO chứa danh sách sản phẩm cần tạo
   * @param userId ID của người dùng hiện tại
   * @returns Kết quả tạo batch với thông tin thành công và thất bại
   */
  async batchCreateProducts(
    batchCreateDto: { products: BusinessCreateProductDto[] },
    userId: number,
  ): Promise<any> {
    try {
      this.logger.log(`Bắt đầu tạo batch ${batchCreateDto.products.length} sản phẩm cho userId=${userId}`);

      const successProducts: any[] = [];
      const failedProducts: Array<{
        index: number;
        productName: string;
        error: string;
      }> = [];

      // Xử lý từng sản phẩm một cách tuần tự để tránh conflict
      for (let i = 0; i < batchCreateDto.products.length; i++) {
        const productDto = batchCreateDto.products[i];

        try {
          this.logger.log(`Đang tạo sản phẩm ${i + 1}/${batchCreateDto.products.length}: ${productDto.name}`);

          // Convert sang type-safe DTO và tạo sản phẩm
          const typeSafeDto = this.convertToTypeSafeDto(productDto);
          const createdProduct = await this.createProduct(typeSafeDto, userId);

          successProducts.push(createdProduct);
          this.logger.log(`Tạo thành công sản phẩm: ${productDto.name}`);

        } catch (error) {
          this.logger.error(`Lỗi khi tạo sản phẩm ${productDto.name}: ${error.message}`, error.stack);

          // Lấy thông điệp lỗi từ AppException hoặc lỗi khác
          const errorMessage = error instanceof AppException
            ? error.message
            : `Lỗi không xác định: ${error.message}`;

          failedProducts.push({
            index: i,
            productName: productDto.name,
            error: errorMessage
          });
        }
      }

      const result = {
        successProducts,
        failedProducts,
        totalProducts: batchCreateDto.products.length,
        successCount: successProducts.length,
        failedCount: failedProducts.length
      };

      this.logger.log(`Hoàn thành batch create: ${result.successCount}/${result.totalProducts} thành công`);

      return result;

    } catch (error) {
      this.logger.error(`Lỗi khi tạo batch sản phẩm: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `Lỗi khi tạo batch sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật sản phẩm
   * @param id ID của sản phẩm cần cập nhật
   * @param updateProductDto DTO chứa thông tin cập nhật
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm đã cập nhật
   */
  @Transactional()
  async updateProduct(
    id: number,
    updateProductDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<ProductResponseDto> {
    try {
      // Tìm sản phẩm theo ID và ID người dùng
      const product = await this.userProductRepository.findByIdAndUserId(
        id,
        userId,
      );

      // Kiểm tra sản phẩm tồn tại
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm với ID ${id}`,
        );
      }

      // Cập nhật các trường được cung cấp
      if (updateProductDto.name !== undefined) {
        product.name = updateProductDto.name;
      }

      if (updateProductDto.productType !== undefined) {
        product.productType = updateProductDto.productType;
      }

      // Xử lý đặc biệt cho EVENT products
      const finalProductType = updateProductDto.productType !== undefined ? updateProductDto.productType : product.productType;

      if (finalProductType === ProductTypeEnum.EVENT) {
        // Đối với EVENT products, tự động set price và giữ typePrice mặc định
        product.price = null;
        product.typePrice = product.typePrice || PriceTypeEnum.HAS_PRICE; // Giữ mặc định HAS_PRICE
        this.logger.log('EVENT product detected in update - setting price to null and keeping typePrice as HAS_PRICE');
      } else {
        // Đối với các loại sản phẩm khác, kiểm tra và cập nhật price/typePrice nếu có
        if (updateProductDto.price !== undefined && updateProductDto.typePrice !== undefined) {
          this.validationHelper.validateProductPrice(updateProductDto.price, updateProductDto.typePrice);
          product.price = updateProductDto.price;
          product.typePrice = updateProductDto.typePrice;
        } else if (updateProductDto.price !== undefined) {
          product.price = updateProductDto.price;
        } else if (updateProductDto.typePrice !== undefined) {
          product.typePrice = updateProductDto.typePrice;
        }
      }

      if (updateProductDto.description !== undefined) {
        product.description = updateProductDto.description;
      }

      // Xử lý thao tác với ảnh
      const imagesUploadUrls: Array<{ url: string; key: string; index: number }> = [];
      const now = Date.now();

      // Xử lý hình ảnh theo 3 cách: imagesMediaTypes, imageOperations, hoặc images (deprecated)
      await this.processImageUpdates(updateProductDto, product, imagesUploadUrls, now);

      if (updateProductDto.tags !== undefined) {
        product.tags = updateProductDto.tags;
      }

      if (updateProductDto.shipmentConfig !== undefined) {
        product.shipmentConfig = updateProductDto.shipmentConfig;
      }

      // Xử lý custom fields nếu có
      if (updateProductDto.customFields !== undefined) {
        let customFields: any[] = [];
        if (updateProductDto.customFields && updateProductDto.customFields.length > 0) {
          this.logger.log(`Xử lý ${updateProductDto.customFields.length} custom fields cho sản phẩm`);

          // Lấy danh sách ID custom fields
          const customFieldIds = this.metadataHelper.extractCustomFieldIds(updateProductDto.customFields);

          // Lấy thông tin chi tiết custom fields từ database
          customFields = await this.customFieldRepository.findByIds(customFieldIds);

          // Validate custom fields
          this.metadataHelper.validateCustomFieldInputs(updateProductDto.customFields, customFields);
        }

        // Xử lý variant metadata cho sản phẩm số
        let additionalMetadata: any = {};

        // Giữ lại variant metadata hiện tại nếu có
        if (product.metadata?.variants) {
          additionalMetadata.variants = product.metadata.variants;
        }

        // Giữ lại service metadata hiện tại nếu có
        if (product.metadata?.serviceTime !== undefined) {
          additionalMetadata.serviceTime = product.metadata.serviceTime;
        }
        if (product.metadata?.serviceDuration !== undefined) {
          additionalMetadata.serviceDuration = product.metadata.serviceDuration;
        }
        if (product.metadata?.serviceProvider !== undefined) {
          additionalMetadata.serviceProvider = product.metadata.serviceProvider;
        }
        if (product.metadata?.serviceType !== undefined) {
          additionalMetadata.serviceType = product.metadata.serviceType;
        }
        if (product.metadata?.serviceLocation !== undefined) {
          additionalMetadata.serviceLocation = product.metadata.serviceLocation;
        }

        // Cập nhật variant metadata nếu có trong advancedInfo
        const digitalAdvancedInfo = updateProductDto.advancedInfo as any;
        if (digitalAdvancedInfo?.variantMetadata?.variants &&
            product.productType === ProductTypeEnum.DIGITAL) {
          this.logger.log(`Cập nhật variant metadata cho sản phẩm số với ${digitalAdvancedInfo.variantMetadata.variants.length} phiên bản`);

          // Xử lý variants: merge với dữ liệu hiện có, loại bỏ imagesMediaTypes và xử lý custom fields
          const processedVariants = await Promise.all(
            digitalAdvancedInfo.variantMetadata.variants.map(async (variant: any, index: number) => {
              const existingVariant = product.metadata?.variants?.[index];
              const processedVariant = { ...variant };

              // Xử lý imageOperations trong variants
              const imageOperations = variant.imageOperations || variant.images;
              if (imageOperations && Array.isArray(imageOperations)) {
                // Bắt đầu với images hiện có
                let currentImages = existingVariant?.images ? [...existingVariant.images] : [];

                // Xử lý DELETE operations
                const deleteOperations = imageOperations.filter((op: any) => op.operation === 'DELETE');
                for (const deleteOp of deleteOperations) {
                  if (deleteOp.key) {
                    // Xóa theo key
                    currentImages = currentImages.filter((imageKey: string) => imageKey !== deleteOp.key);
                    this.logger.debug(`Xóa variant image với key: ${deleteOp.key}`);
                  }
                }

                // Xử lý ADD operations - chỉ lưu placeholder, sẽ được thay thế bằng S3 keys sau
                const addOperations = imageOperations.filter((op: any) => op.operation === 'ADD');
                for (let i = 0; i < addOperations.length; i++) {
                  // Tạo placeholder key sẽ được thay thế sau khi upload
                  const placeholderKey = `variant-${index}-image-${i}-${now}`;
                  currentImages.push(placeholderKey);
                }

                processedVariant.images = currentImages;
              } else if (existingVariant?.images && !variant.imagesMediaTypes) {
                // Giữ lại images hiện có nếu không có operations mới
                processedVariant.images = existingVariant.images;
              }

              // Xử lý custom fields cho variant nếu có
              if (variant.customFields && variant.customFields.length > 0) {
                this.logger.log(`Cập nhật ${variant.customFields.length} custom fields cho variant ${variant.name}`);

                // Lấy danh sách ID custom fields
                const variantCustomFieldIds = this.metadataHelper.extractCustomFieldIds(variant.customFields);

                // Lấy thông tin chi tiết custom fields từ database
                const variantCustomFields = await this.customFieldRepository.findByIds(variantCustomFieldIds);

                // Validate custom fields
                this.metadataHelper.validateCustomFieldInputs(variant.customFields, variantCustomFields);

                // Build metadata cho variant custom fields
                const variantMetadata = this.metadataHelper.buildMetadataCustomFields(
                  variant.customFields,
                  variantCustomFields
                );

                processedVariant.customFields = variantMetadata;
              } else if (existingVariant?.customFields) {
                // Giữ lại custom fields hiện có nếu không có custom fields mới
                processedVariant.customFields = existingVariant.customFields;
              }

              // Loại bỏ imagesMediaTypes, images và imageOperations khỏi metadata (chỉ dùng để tạo upload URLs)
              delete processedVariant.imagesMediaTypes;
              delete processedVariant.imageOperations;

              return processedVariant;
            })
          );

          additionalMetadata.variants = processedVariants;
        }

        // Cập nhật service metadata nếu có
        if (product.productType === ProductTypeEnum.SERVICE) {
          if (updateProductDto.serviceTime !== undefined) {
            additionalMetadata.serviceTime = updateProductDto.serviceTime;
          }

          if (updateProductDto.serviceDuration !== undefined) {
            additionalMetadata.serviceDuration = updateProductDto.serviceDuration;
          }

          if (updateProductDto.serviceProvider !== undefined) {
            additionalMetadata.serviceProvider = updateProductDto.serviceProvider;
          }

          if (updateProductDto.serviceType !== undefined) {
            additionalMetadata.serviceType = updateProductDto.serviceType;
          }

          if (updateProductDto.serviceLocation !== undefined) {
            additionalMetadata.serviceLocation = updateProductDto.serviceLocation;
          }

          this.logger.log(`Cập nhật service metadata cho sản phẩm dịch vụ`);
        }

        // Cập nhật metadata cho sản phẩm
        const metadata = this.metadataHelper.buildMetadata(
          updateProductDto.customFields,
          customFields,
          additionalMetadata
        );
        product.metadata = metadata;
      }

      // Đảm bảo các trường embedding vẫn là null để tránh lỗi vector dimension
      product.nameEmbedding = null;
      product.descriptionEmbedding = null;
      product.tagsEmbedding = null;

      // Cập nhật thời gian cập nhật
      product.updatedAt = Date.now();

      // Lưu sản phẩm vào database
      const updatedProduct = await this.userProductRepository.save(product);

      // Xử lý thông tin nâng cao nếu có
      let advancedImagesUploadUrls: Array<{ url: string; key: string; type: string; index: number; position: number }> = [];
      if (updateProductDto.advancedInfo) {
        // Kiểm tra productType có hỗ trợ advancedInfo không
        if (product.productType === ProductTypeEnum.PHYSICAL) {
          throw new AppException(
            BUSINESS_ERROR_CODES.INVALID_INPUT,
            'Sản phẩm vật lý không được có thông tin nâng cao'
          );
        }

        try {
          // Validate advancedInfo theo productType từ database
          this.productValidationHelper.validateAdvancedInfo(product.productType, updateProductDto.advancedInfo);

          // Tạo presigned URLs cho advanced images trước khi cập nhật advanced info
          advancedImagesUploadUrls = await this.createAdvancedImagesUploadUrls(
            updateProductDto.advancedInfo,
            product.productType,
            now
          );

          const advancedInfo = await this.updateAdvancedInfo(updatedProduct.id, product.productType, updateProductDto.advancedInfo);

          // Cập nhật detail_id trong user_product nếu chưa có
          if (advancedInfo && advancedInfo.id && !updatedProduct.detail_id) {
            updatedProduct.detail_id = advancedInfo.id;
            await this.userProductRepository.save(updatedProduct);
            this.logger.log(`Updated product ${updatedProduct.id} with detail_id: ${advancedInfo.id} during update`);
          }

          // Cập nhật advanced info với S3 keys nếu có advanced images
          if (advancedImagesUploadUrls.length > 0) {
            if (product.productType === ProductTypeEnum.DIGITAL) {
              await this.updateVariantMetadataWithImageKeys(updatedProduct, advancedImagesUploadUrls);
            } else if (product.productType === ProductTypeEnum.EVENT || product.productType === ProductTypeEnum.SERVICE) {
              this.logger.log(`Gọi updateAdvancedInfoWithImageKeys với advancedInfo.id=${advancedInfo.id}, productType=${product.productType}, uploadUrls=${advancedImagesUploadUrls.length}`);
              await this.updateAdvancedInfoWithImageKeys(advancedInfo.id, product.productType, advancedImagesUploadUrls);
            }
          }
        } catch (advancedInfoError) {
          // Ghi log nhưng không dừng việc cập nhật sản phẩm
          this.logger.warn(`Không thể cập nhật thông tin nâng cao cho sản phẩm ${updatedProduct.id}: ${advancedInfoError.message}`);
        }
      }

      // Xử lý imageOperations cho ticket types/service packages khi không có advancedInfo trong request
      // nhưng có imageOperations và sản phẩm đã có advanced info
      if (!updateProductDto.advancedInfo &&
          updateProductDto.imageOperations &&
          updateProductDto.imageOperations.length > 0 &&
          updatedProduct.detail_id &&
          (product.productType === ProductTypeEnum.EVENT || product.productType === ProductTypeEnum.SERVICE)) {
        try {
          this.logger.log(`Xử lý imageOperations cho ${product.productType} product với ${updateProductDto.imageOperations.length} operations`);

          // Lấy advanced info hiện tại
          const currentAdvancedInfo = await this.productAdvancedInfoRepository.findOne({
            where: { id: updatedProduct.detail_id }
          });

          if (currentAdvancedInfo) {
            // Xử lý theo đúng thứ tự: DELETE trước, ADD sau (như logic sản phẩm vật lý/số)
            await this.processAdvancedImageOperations(
              updateProductDto.imageOperations,
              currentAdvancedInfo,
              product.productType,
              advancedImagesUploadUrls,
              now
            );
          }
        } catch (imageOperationsError) {
          this.logger.warn(`Không thể xử lý imageOperations cho sản phẩm ${updatedProduct.id}: ${imageOperationsError.message}`);
        }
      }

      // Xử lý images (deprecated) cho ticket types/service packages khi không có advancedInfo trong request
      // nhưng có images và sản phẩm đã có advanced info
      if (!updateProductDto.advancedInfo &&
          updateProductDto.images &&
          updateProductDto.images.length > 0 &&
          updatedProduct.detail_id &&
          (product.productType === ProductTypeEnum.EVENT || product.productType === ProductTypeEnum.SERVICE)) {
        // Kiểm tra xem có phải images cho advanced không (không có flag isProductImageOperations)
        const isAdvancedImages = (updateProductDto as any).isProductImageOperations !== true;

        if (isAdvancedImages) {
          try {
            this.logger.log(`Xử lý images cho ${product.productType} advanced info với ${updateProductDto.images.length} operations`);

            // Lấy advanced info hiện tại
            const currentAdvancedInfo = await this.productAdvancedInfoRepository.findOne({
              where: { id: updatedProduct.detail_id }
            });

            if (currentAdvancedInfo) {
              // Chuyển đổi images thành format imageOperations để tái sử dụng logic
              const imageOperations = updateProductDto.images.map(img => ({
                operation: img.operation,
                key: img.key,
                mimeType: img.mimeType,
                position: img.position
              }));

              // Xử lý theo đúng thứ tự: DELETE trước, ADD sau
              await this.processAdvancedImageOperations(
                imageOperations,
                currentAdvancedInfo,
                product.productType,
                advancedImagesUploadUrls,
                now
              );
            }
          } catch (imagesError) {
            this.logger.warn(`Không thể xử lý images cho advanced info của sản phẩm ${updatedProduct.id}: ${imagesError.message}`);
          }
        }
      }

      // Xử lý phân loại sản phẩm nếu có
      let classifications: ClassificationResponseDto[] = [];
      const classificationUploadUrls: any[] = [];
      if (updateProductDto.classifications && updateProductDto.classifications.length > 0) {
        try {
          // Xử lý từng phân loại
          for (const classificationDto of updateProductDto.classifications) {
            if (classificationDto.id) {
              // Cập nhật phân loại hiện có
              const classification = await this.classificationService.update(
                classificationDto.id,
                classificationDto,
                userId
              );

              // Tách riêng upload URLs của classification
              this.logger.log(`Classification update response: ${JSON.stringify(classification)}`);
              if (classification.uploadUrls && classification.uploadUrls.imagesUploadUrls) {
                this.logger.log(`Found ${classification.uploadUrls.imagesUploadUrls.length} upload URLs for classification ${classificationDto.id}`);
                classificationUploadUrls.push(...classification.uploadUrls.imagesUploadUrls);
                // Xóa uploadUrls khỏi classification response để tránh trùng lặp
                delete classification.uploadUrls;
              } else {
                this.logger.log(`No upload URLs found for classification ${classificationDto.id}`);
              }

              classifications.push(classification);
            } else {
              // Tạo phân loại mới
              // Kiểm tra type bắt buộc cho classification mới
              if (!classificationDto.type) {
                throw new AppException(
                  BUSINESS_ERROR_CODES.INVALID_INPUT,
                  'Loại phân loại (type) là bắt buộc khi tạo phân loại mới',
                );
              }

              // Chuyển đổi từ UpdateClassificationDto sang CreateClassificationDto
              const createClassificationDto: CreateClassificationDto = {
                type: classificationDto.type,
                price: classificationDto.price,
                customFields: classificationDto.customFields,
                imagesMediaTypes: classificationDto.imagesMediaTypes,
                imageOperations: classificationDto.imageOperations,
              };

              const classification = await this.classificationService.create(
                id,
                createClassificationDto,
                userId,
              );

              // Tách riêng upload URLs của classification
              if (classification.uploadUrls && classification.uploadUrls.imagesUploadUrls) {
                classificationUploadUrls.push(...classification.uploadUrls.imagesUploadUrls);
                // Xóa uploadUrls khỏi classification response để tránh trùng lặp
                delete classification.uploadUrls;
              }

              classifications.push(classification);
            }
          }
        } catch (classificationError) {
          // Ghi log nhưng không dừng việc cập nhật sản phẩm
          this.logger.warn(`Không thể cập nhật phân loại cho sản phẩm ${id}: ${classificationError.message}`);
        }
      }

      // Xử lý xóa phân loại sản phẩm nếu có
      if (updateProductDto.classificationsToDelete && updateProductDto.classificationsToDelete.length > 0) {
        try {
          for (const classificationId of updateProductDto.classificationsToDelete) {
            await this.classificationService.delete(classificationId, userId);
          }
          this.logger.log(`Đã xóa ${updateProductDto.classificationsToDelete.length} phân loại cho sản phẩm ${id}`);
        } catch (classificationDeleteError) {
          // Ghi log nhưng không dừng việc cập nhật sản phẩm
          this.logger.warn(`Không thể xóa phân loại cho sản phẩm ${id}: ${classificationDeleteError.message}`);
        }
      }

      // Validate inventory cho sản phẩm có thể có inventory - Kiểm tra xem có inventory hiện tại không
      // DIGITAL, SERVICE, EVENT và COMBO products không cần inventory
      // COMBO không cần inventory vì nó là tập hợp các sản phẩm khác đã có inventory riêng
      if (updatedProduct.productType === ProductTypeEnum.PHYSICAL) {
        // Kiểm tra xem có inventory hiện tại không
        const existingInventoriesResult = await this.inventoryRepository.findAll({ productId: updatedProduct.id, limit: 1 });
        const hasExistingInventory = existingInventoriesResult.items && existingInventoriesResult.items.length > 0;

        // Nếu không có inventory hiện tại và cũng không có inventory trong request, báo lỗi
        if (!hasExistingInventory && !updateProductDto.inventory) {
          throw new AppException(
            BUSINESS_ERROR_CODES.INVENTORY_VALIDATION_FAILED,
            `Thông tin tồn kho là bắt buộc đối với sản phẩm loại ${updatedProduct.productType}`
          );
        }
      }

      // Xử lý cập nhật tồn kho nếu có thông tin inventory
      let inventory: InventoryResponseDto | undefined;
      if (updateProductDto.inventory) {
        try {
          this.logger.log(`Bắt đầu cập nhật inventory cho sản phẩm ${id} với dữ liệu: ${JSON.stringify(updateProductDto.inventory)}`);

          // Kiểm tra xem có sử dụng inventory đã có không
          if (updateProductDto.inventory.inventoryId) {
            // Sử dụng inventory đã có
            inventory = await this.useExistingInventory(
              updatedProduct.id,
              updateProductDto.inventory.inventoryId,
              userId
            );
            this.logger.log(`Sử dụng inventory có sẵn thành công cho sản phẩm ${id}: ${JSON.stringify(inventory)}`);
          } else {
            // Tạo/cập nhật inventory
            inventory = await this.createOrUpdateProductInventory(
              updatedProduct.id,
              updateProductDto.inventory,
              userId
            );
            this.logger.log(`Cập nhật inventory thành công cho sản phẩm ${id}: ${JSON.stringify(inventory)}`);
          }
        } catch (inventoryError) {
          // Log chi tiết lỗi inventory
          this.logger.error(`Lỗi khi cập nhật tồn kho cho sản phẩm ${id}: ${inventoryError.message}`, inventoryError.stack);

          // Nếu là lỗi validation quan trọng, throw lỗi để user biết
          if (inventoryError instanceof AppException &&
              (inventoryError.message.includes('SKU') && inventoryError.message.includes('đã tồn tại') ||
               inventoryError.message.includes('Inventory') && inventoryError.message.includes('không tồn tại') ||
               inventoryError.message.includes('đã được sử dụng cho sản phẩm khác'))) {
            throw inventoryError;
          }

          // Các lỗi khác chỉ log warning và tiếp tục
          this.logger.warn(`Không thể cập nhật tồn kho cho sản phẩm ${id}: ${inventoryError.message}`);
        }
      }

      // Chuyển đổi thành DTO response và trả về
      const productResponse = await this.userProductHelper.mapToProductResponseDto(updatedProduct);

      // Thêm thông tin URL upload, phân loại và tồn kho vào response
      const response: any = { ...productResponse };

      // Thêm advanced info nếu có (để hiển thị ticket types với images)
      if (updatedProduct.detail_id) {
        try {
          const advancedInfo = await this.productAdvancedInfoRepository.findOne({
            where: { id: updatedProduct.detail_id }
          });
          if (advancedInfo) {
            this.logger.log(`Retrieved advanced info for response: ticketTypes=${advancedInfo.ticketTypes ? JSON.stringify(advancedInfo.ticketTypes) : 'null'}`);

            // Xử lý advanced info theo loại sản phẩm với images
            switch (updatedProduct.productType) {
              case ProductTypeEnum.EVENT:
                response.advancedInfo = {
                  purchaseCount: advancedInfo.purchaseCount,
                  eventFormat: advancedInfo.eventFormat,
                  eventLink: advancedInfo.eventLink,
                  eventLocation: advancedInfo.eventLocation,
                  startDate: advancedInfo.startDate,
                  endDate: advancedInfo.endDate,
                  timezone: advancedInfo.timezone,
                  ticketTypes: await this.processTicketTypesWithImages(advancedInfo.ticketTypes || [])
                };
                break;

              case ProductTypeEnum.SERVICE:
                response.advancedInfo = {
                  purchaseCount: advancedInfo.purchaseCount,
                  servicePackages: await this.processServicePackagesWithImages(advancedInfo.servicePackages || [])
                };
                break;

              case ProductTypeEnum.DIGITAL:
                response.advancedInfo = {
                  purchaseCount: advancedInfo.purchaseCount,
                  digitalFulfillmentFlow: advancedInfo.digitalFulfillmentFlow,
                  digitalOutput: advancedInfo.digitalOutput
                };
                break;

              case ProductTypeEnum.COMBO:
                response.advancedInfo = {
                  purchaseCount: advancedInfo.purchaseCount,
                  combo: advancedInfo.combo
                };
                break;

              default:
                response.advancedInfo = {
                  purchaseCount: advancedInfo.purchaseCount
                };
            }
          }
        } catch (advancedInfoError) {
          this.logger.warn(`Không thể lấy advanced info cho response: ${advancedInfoError.message}`);
        }
      }

      // Thêm URL upload nếu có
      if (imagesUploadUrls.length > 0 || advancedImagesUploadUrls.length > 0 || classificationUploadUrls.length > 0) {
        response.uploadUrls = {
          productId: updatedProduct.id.toString(),
          ...(imagesUploadUrls.length > 0 ? { imagesUploadUrls } : {}),
          ...(advancedImagesUploadUrls.length > 0 ? { advancedImagesUploadUrls } : {}),
          ...(classificationUploadUrls.length > 0 ? { classificationUploadUrls } : {})
        };
      }

      // Thêm phân loại nếu có
      if (classifications.length > 0) {
        response.classifications = classifications;
      }

      // Thêm tồn kho nếu có (từ logic update) hoặc lấy từ database
      // Chỉ PHYSICAL products cần inventory
      if (inventory) {
        response.inventory = inventory;
      } else if (updatedProduct.productType === ProductTypeEnum.PHYSICAL) {
        // Nếu không có inventory từ update logic, lấy từ database như API GET
        try {
          const inventoriesResult = await this.inventoryRepository.findAll({ productId: updatedProduct.id, limit: 1 });
          if (inventoriesResult.items && inventoriesResult.items.length > 0) {
            const existingInventory = inventoriesResult.items[0];

            // Chuyển đổi sang DTO response
            const inventoryDto = plainToInstance(InventoryResponseDto, existingInventory, {
              excludeExtraneousValues: true,
            });

            // Lấy thông tin warehouse nếu có
            if (existingInventory.warehouseId) {
              try {
                const warehouse = await this.physicalWarehouseRepository.findByWarehouseIdWithDetails(existingInventory.warehouseId);
                if (warehouse) {
                  inventoryDto.warehouse = {
                    id: warehouse.id,
                    warehouseId: warehouse.warehouseId,
                    name: warehouse.name,
                    description: warehouse.description,
                    type: warehouse.type,
                    address: warehouse.address,
                    capacity: warehouse.capacity,
                  };
                }
              } catch (warehouseError) {
                this.logger.warn(`Không thể lấy thông tin warehouse cho inventory ${existingInventory.id}: ${warehouseError.message}`);
              }
            }

            response.inventory = inventoryDto;
          }
        } catch (inventoryError) {
          this.logger.warn(`Không thể lấy thông tin tồn kho cho sản phẩm ${updatedProduct.id}: ${inventoryError.message}`);
        }
      }

      return response;
    } catch (error) {
      // Nếu là AppException, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác, ghi log và ném lỗi chung
      this.logger.error(
        `Lỗi khi cập nhật sản phẩm: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_UPDATE_FAILED,
        `Lỗi khi cập nhật sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Tạo presigned URL cho việc tải lên hình ảnh sản phẩm
   * @param fileName Tên file
   * @param mediaType Loại media
   * @returns Thông tin URL và key
   */
  private async createImageUploadUrl(
    fileName: string,
    mediaType: ImageTypeEnum = ImageTypeEnum.PNG,
  ): Promise<{ key: string; url: string }> {
    try {
      // Tạo S3 key cho hình ảnh sản phẩm
      // Sử dụng cấu trúc thư mục giống với knowledge files và marketplace
      const key = generateS3Key({
        baseFolder: 'business',
        categoryFolder: CategoryFolderEnum.IMAGE,
        fileName: fileName || 'product-image',
        useTimeFolder: true,
      });

      // Tạo presigned URL
      const url = await this.s3Service.createPresignedWithID(
        key,
        TimeIntervalEnum.FIFTEEN_MINUTES,
        mediaType,
        FileSizeEnum.FIVE_MB,
      );

      return { key, url };
    } catch (error) {
      this.logger.error(`Lỗi khi tạo URL upload hình ảnh: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `Lỗi khi tạo URL upload hình ảnh: ${error.message}`,
      );
    }
  }

  /**
   * Xóa sản phẩm (soft delete)
   * @param id ID của sản phẩm cần xóa
   * @param userId ID của người dùng hiện tại
   */
  @Transactional()
  async deleteProduct(id: number, userId: number): Promise<void> {
    try {
      // Tìm sản phẩm theo ID và ID người dùng
      const product = await this.userProductRepository.findByIdAndUserId(
        id,
        userId,
      );

      // Kiểm tra sản phẩm tồn tại
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm với ID ${id}`,
        );
      }

      // Cập nhật trạng thái sản phẩm thành DELETED
      product.status = EntityStatusEnum.DELETED;
      product.updatedAt = Date.now();

      // Lưu sản phẩm vào database
      await this.userProductRepository.save(product);
    } catch (error) {
      // Nếu là AppException, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác, ghi log và ném lỗi chung
      this.logger.error(`Lỗi khi xóa sản phẩm: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_DELETION_FAILED,
        `Lỗi khi xóa sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Xóa nhiều sản phẩm (soft delete)
   * @param bulkDeleteDto DTO chứa danh sách ID sản phẩm cần xóa
   * @param userId ID của người dùng hiện tại
   * @returns Kết quả xóa nhiều sản phẩm
   */
  @Transactional()
  async bulkDeleteProducts(
    bulkDeleteDto: BulkDeleteProductDto,
    userId: number,
  ): Promise<BulkDeleteProductResponseDto> {
    try {
      const { productIds } = bulkDeleteDto;
      const results: any[] = [];
      let successCount = 0;
      let failureCount = 0;

      this.logger.log(
        `Bắt đầu xóa bulk ${productIds.length} sản phẩm cho userId=${userId}`,
      );

      // Xử lý từng sản phẩm một để có thể báo cáo chi tiết
      for (const productId of productIds) {
        try {
          // Tìm sản phẩm theo ID và ID người dùng
          const product = await this.userProductRepository.findByIdAndUserId(
            productId,
            userId,
          );

          // Kiểm tra sản phẩm tồn tại
          if (!product) {
            results.push({
              productId,
              status: 'error',
              message: `Không tìm thấy sản phẩm với ID ${productId}`,
            });
            failureCount++;
            continue;
          }

          // Cập nhật trạng thái sản phẩm thành DELETED
          product.status = EntityStatusEnum.DELETED;
          product.updatedAt = Date.now();

          // Lưu sản phẩm vào database
          await this.userProductRepository.save(product);

          results.push({
            productId,
            status: 'success',
            message: 'Xóa sản phẩm thành công',
          });
          successCount++;

        } catch (error) {
          this.logger.error(
            `Lỗi khi xóa sản phẩm ${productId}: ${error.message}`,
            error.stack,
          );

          results.push({
            productId,
            status: 'error',
            message: error instanceof AppException ? error.message : `Lỗi khi xóa sản phẩm: ${error.message}`,
          });
          failureCount++;
        }
      }

      const response: BulkDeleteProductResponseDto = {
        totalRequested: productIds.length,
        successCount,
        failureCount,
        results,
        message: `Xóa thành công ${successCount}/${productIds.length} sản phẩm`,
      };

      this.logger.log(
        `Hoàn thành xóa bulk sản phẩm: ${successCount} thành công, ${failureCount} thất bại`,
      );

      return response;
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa bulk sản phẩm: ${error.message}`,
        error.stack,
      );

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_DELETION_FAILED,
        `Lỗi khi xóa bulk sản phẩm: ${error.message}`,
      );
    }
  }

  // ==================== INVENTORY MANAGEMENT METHODS ====================

  /**
   * Lấy thông tin tồn kho của sản phẩm theo kho
   * @param productId ID sản phẩm
   * @param warehouseId ID kho (optional)
   * @param userId ID người dùng
   * @returns Thông tin tồn kho
   */
  @Transactional()
  async getProductInventory(
    productId: number,
    warehouseId: number | null,
    userId: number,
  ): Promise<InventoryResponseDto[]> {
    try {
      // Kiểm tra sản phẩm có tồn tại và thuộc về user
      const product = await this.userProductRepository.findByIdAndUserId(productId, userId);
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          'Sản phẩm không tồn tại hoặc không thuộc về bạn',
        );
      }

      // Tạo query để lấy inventory
      const queryDto = new QueryInventoryDto();
      queryDto.productId = productId;
      queryDto.userId = userId;
      if (warehouseId) {
        queryDto.warehouseId = warehouseId;
      }

      // Lấy danh sách inventory
      const inventoryResult = await this.inventoryRepository.findAll(queryDto);

      // Chuyển đổi sang DTO response
      const inventoryDtos = await Promise.all(
        inventoryResult.items.map(async (inventory) => {
          const dto = new InventoryResponseDto();
          dto.id = inventory.id;
          dto.productId = inventory.productId;
          dto.warehouseId = inventory.warehouseId;
          dto.currentQuantity = inventory.currentQuantity;
          dto.totalQuantity = inventory.totalQuantity;
          dto.availableQuantity = inventory.availableQuantity;
          dto.reservedQuantity = inventory.reservedQuantity;
          dto.defectiveQuantity = inventory.defectiveQuantity;
          dto.lastUpdated = inventory.lastUpdated;

          // Lấy thông tin warehouse nếu có
          if (inventory.warehouseId) {
            const warehouse = await this.physicalWarehouseRepository.findByWarehouseIdWithDetails(inventory.warehouseId);
            if (warehouse) {
              dto.warehouse = {
                id: warehouse.id,
                warehouseId: warehouse.warehouseId,
                name: warehouse.name,
                description: warehouse.description,
                type: warehouse.type,
                address: warehouse.address,
                capacity: warehouse.capacity,
              };
            }
          }

          return dto;
        })
      );

      return inventoryDtos;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi lấy thông tin tồn kho sản phẩm ${productId}: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.INVENTORY_FETCH_FAILED,
        `Lỗi khi lấy thông tin tồn kho: ${error.message}`,
      );
    }
  }

  /**
   * Sử dụng inventory đã có cho sản phẩm
   * @param productId ID của sản phẩm
   * @param inventoryId ID của inventory đã có
   * @param userId ID của người dùng
   * @returns Thông tin tồn kho
   */
  @Transactional()
  private async useExistingInventory(
    productId: number,
    inventoryId: number,
    userId: number,
  ): Promise<InventoryResponseDto> {
    try {
      // Kiểm tra inventory có tồn tại không
      const existingInventory = await this.inventoryRepository.findOne({
        where: { id: inventoryId }
      });

      if (!existingInventory) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVENTORY_NOT_FOUND,
          `Inventory với ID ${inventoryId} không tồn tại`
        );
      }

      // Kiểm tra inventory đã được sử dụng cho sản phẩm khác chưa
      if (existingInventory.productId && existingInventory.productId !== productId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVENTORY_ALREADY_USED,
          `Inventory với ID ${inventoryId} đã được sử dụng cho sản phẩm khác`
        );
      }

      // Cập nhật productId cho inventory nếu chưa có
      if (!existingInventory.productId) {
        existingInventory.productId = productId;
        await this.inventoryRepository.save(existingInventory);
      }

      // Chuyển đổi sang DTO response
      return plainToInstance(InventoryResponseDto, existingInventory, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(`Lỗi khi sử dụng inventory có sẵn ${inventoryId} cho sản phẩm ${productId}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.INVENTORY_UPDATE_FAILED,
        `Lỗi khi sử dụng inventory có sẵn: ${error.message}`
      );
    }
  }

  /**
   * Tạo hoặc cập nhật tồn kho cho sản phẩm
   * @param productId ID sản phẩm
   * @param inventoryData Dữ liệu tồn kho
   * @param userId ID người dùng
   * @returns Thông tin tồn kho đã tạo/cập nhật
   */
  @Transactional()
  async createOrUpdateProductInventory(
    productId: number,
    inventoryData: ProductInventoryDto,
    userId: number,
  ): Promise<InventoryResponseDto> {
    try {
      // Kiểm tra sản phẩm có tồn tại và thuộc về user
      const product = await this.userProductRepository.findByIdAndUserId(productId, userId);
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          'Sản phẩm không tồn tại hoặc không thuộc về bạn',
        );
      }

      // Kiểm tra warehouse có tồn tại
      if (inventoryData.warehouseId) {
        this.logger.log(`Kiểm tra warehouse với ID: ${inventoryData.warehouseId}`);

        // Kiểm tra warehouse tồn tại trong bảng warehouse trước
        const warehouseGeneral = await this.dataSource.getRepository('warehouse')
          .createQueryBuilder('w')
          .where('w.warehouse_id = :warehouseId', { warehouseId: inventoryData.warehouseId })
          .getOne();

        if (!warehouseGeneral) {
          throw new AppException(
            BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND,
            `Kho với ID ${inventoryData.warehouseId} không tồn tại trong hệ thống`,
          );
        }

        this.logger.log(`Warehouse general tồn tại: ${JSON.stringify(warehouseGeneral)}`);

        // Kiểm tra physical warehouse
        const warehouse = await this.physicalWarehouseRepository.findByWarehouseId_user(inventoryData.warehouseId);
        if (!warehouse) {
          throw new AppException(
            BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND,
            `Kho vật lý với ID ${inventoryData.warehouseId} không tồn tại trong bảng physical_warehouse`,
          );
        }

        this.logger.log(`Physical warehouse tồn tại: ${JSON.stringify(warehouse)}`);
      }

      // Kiểm tra SKU không được trùng với sản phẩm khác của user đang đăng nhập
      if (inventoryData.sku) {
        const existingInventoryWithSku = await this.inventoryRepository.findBySkuAndUserId(inventoryData.sku, userId);
        if (existingInventoryWithSku && existingInventoryWithSku.productId !== productId) {
          throw new AppException(
            BUSINESS_ERROR_CODES.INVENTORY_CREATION_FAILED,
            `Mã SKU "${inventoryData.sku}" đã tồn tại trong sản phẩm khác của bạn`,
          );
        }
      }

      // Kiểm tra xem đã có inventory cho sản phẩm và kho này chưa
      const existingInventory = await this.inventoryRepository.findByProductAndWarehouseNullable(
        productId,
        inventoryData.warehouseId || null,
      );

      let inventory: Inventory;

      if (existingInventory) {
        // Cập nhật inventory hiện có
        existingInventory.availableQuantity = inventoryData.availableQuantity || 0;
        existingInventory.sku = inventoryData.sku || null;
        existingInventory.barcode = inventoryData.barcode || null;

        // Sử dụng helper để tính toán số lượng
        this.validationHelper.calculateInventoryQuantities(existingInventory);
        existingInventory.lastUpdated = Date.now();

        inventory = await this.inventoryRepository.save(existingInventory);
      } else {
        // Tạo inventory mới
        const newInventory = new Inventory();
        newInventory.productId = productId;
        newInventory.warehouseId = inventoryData.warehouseId || null;
        newInventory.availableQuantity = inventoryData.availableQuantity || 0;
        newInventory.sku = inventoryData.sku || null;
        newInventory.barcode = inventoryData.barcode || null;

        // Sử dụng helper để tính toán số lượng
        this.validationHelper.calculateInventoryQuantities(newInventory);
        newInventory.lastUpdated = Date.now();

        inventory = await this.inventoryRepository.save(newInventory);
      }

      // Chuyển đổi sang DTO response
      const dto = new InventoryResponseDto();
      dto.id = inventory.id;
      dto.productId = inventory.productId;
      dto.warehouseId = inventory.warehouseId;
      dto.currentQuantity = inventory.currentQuantity;
      dto.totalQuantity = inventory.totalQuantity;
      dto.availableQuantity = inventory.availableQuantity;
      dto.reservedQuantity = inventory.reservedQuantity;
      dto.defectiveQuantity = inventory.defectiveQuantity;
      dto.lastUpdated = inventory.lastUpdated;
      dto.sku = inventory.sku;
      dto.barcode = inventory.barcode;

      // Lấy thông tin warehouse nếu có
      if (inventory.warehouseId) {
        const warehouse = await this.physicalWarehouseRepository.findByWarehouseIdWithDetails(inventory.warehouseId);
        if (warehouse) {
          dto.warehouse = {
            id: warehouse.id,
            warehouseId: warehouse.warehouseId,
            name: warehouse.name,
            description: warehouse.description,
            type: warehouse.type,
            address: warehouse.address,
            capacity: warehouse.capacity,
          };
        }
      }

      return dto;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi tạo/cập nhật tồn kho sản phẩm ${productId}: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.INVENTORY_CREATION_FAILED,
        `Lỗi khi tạo/cập nhật tồn kho: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách kho vật lý để chọn
   * @returns Danh sách kho vật lý
   */
  async getWarehouseList(): Promise<WarehouseListDto[]> {
    try {
      // Lấy tất cả kho vật lý với thông tin đầy đủ
      const result = await this.physicalWarehouseRepository.findAll({
        page: 1,
        limit: 1000, // Lấy nhiều để có đủ kho cho user chọn
        sortBy: 'warehouseId',
        sortDirection: 'ASC',
      });

      // Chuyển đổi sang DTO
      return result.items.map(warehouse => {
        const dto = new WarehouseListDto();
        dto.warehouseId = warehouse.warehouseId;
        dto.name = warehouse.name;
        dto.description = warehouse.description;
        dto.type = warehouse.type;
        dto.address = warehouse.address;
        dto.capacity = warehouse.capacity;
        return dto;
      });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách kho vật lý: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_FIND_FAILED,
        `Lỗi khi lấy danh sách kho vật lý: ${error.message}`,
      );
    }
  }

  /**
   * Tạo thông tin nâng cao cho sản phẩm
   * @param productId ID sản phẩm
   * @param productType Loại sản phẩm
   * @param advancedInfoData Dữ liệu thông tin nâng cao
   * @param advancedImagesUploadUrls Danh sách URLs upload cho advanced images
   * @returns Thông tin nâng cao đã tạo
   */
  private async createAdvancedInfo(
    productId: number,
    productType: ProductTypeEnum,
    advancedInfoData: any,
    advancedImagesUploadUrls: Array<{ url: string; key: string; type: string; index: number; position: number }> = []
  ): Promise<ProductAdvancedInfo> {
    try {
      this.logger.log(`Tạo thông tin nâng cao cho sản phẩm ${productId}, loại: ${productType}`);

      const advancedInfo = new ProductAdvancedInfo();
      advancedInfo.productId = productId;
      advancedInfo.productType = productType;
      advancedInfo.purchaseCount = advancedInfoData.purchaseCount || 0;

      // Lưu advanced images
      const advancedImages = advancedImagesUploadUrls.map(uploadUrl => ({
        key: uploadUrl.key,
        type: uploadUrl.type,
        index: uploadUrl.index,
        position: uploadUrl.position
      }));
      advancedInfo.images = advancedImages;

      // Xử lý theo từng loại sản phẩm
      switch (productType) {
        case ProductTypeEnum.DIGITAL:
          advancedInfo.digitalFulfillmentFlow = advancedInfoData.digitalFulfillmentFlow;
          advancedInfo.digitalOutput = advancedInfoData.digitalOutput;
          break;

        case ProductTypeEnum.EVENT:
          advancedInfo.eventFormat = advancedInfoData.eventFormat;
          advancedInfo.eventLink = advancedInfoData.eventLink;
          advancedInfo.eventLocation = advancedInfoData.eventLocation;
          advancedInfo.startDate = advancedInfoData.startDate;
          advancedInfo.endDate = advancedInfoData.endDate;
          advancedInfo.timezone = advancedInfoData.timezone;
          advancedInfo.ticketTypes = advancedInfoData.ticketTypes;
          break;

        case ProductTypeEnum.SERVICE:
          advancedInfo.servicePackages = advancedInfoData.servicePackages;
          break;

        case ProductTypeEnum.COMBO:
          // Đối với COMBO, lưu purchaseCount và thông tin combo
          advancedInfo.combo = {
            info: advancedInfoData.info || []
          };
          this.logger.log(`Tạo advanced info cho COMBO product với purchaseCount: ${advancedInfoData.purchaseCount} và ${advancedInfoData.info?.length || 0} sản phẩm`);
          break;

        default:
          throw new Error(`Loại sản phẩm ${productType} không hỗ trợ thông tin nâng cao`);
      }

      const savedAdvancedInfo = await this.productAdvancedInfoRepository.save(advancedInfo);
      this.logger.log(`Tạo thông tin nâng cao thành công cho sản phẩm ${productId}`);

      return savedAdvancedInfo;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo thông tin nâng cao cho sản phẩm ${productId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật thông tin nâng cao cho sản phẩm
   * @param productId ID sản phẩm
   * @param productType Loại sản phẩm
   * @param advancedInfoData Dữ liệu thông tin nâng cao
   * @returns Thông tin nâng cao đã cập nhật
   */
  private async updateAdvancedInfo(
    productId: number,
    productType: ProductTypeEnum,
    advancedInfoData: any
  ): Promise<ProductAdvancedInfo> {
    try {
      this.logger.log(`Cập nhật thông tin nâng cao cho sản phẩm ${productId}, loại: ${productType}`);

      // Tìm thông tin nâng cao hiện có
      let advancedInfo = await this.productAdvancedInfoRepository.findByProductId(productId);

      if (!advancedInfo) {
        // Nếu chưa có, tạo mới
        const newAdvancedInfo = await this.createAdvancedInfo(productId, productType, advancedInfoData);

        // Cập nhật detail_id trong user_product
        const product = await this.userProductRepository.findById(productId);
        if (product && newAdvancedInfo && newAdvancedInfo.id) {
          product.detail_id = newAdvancedInfo.id;
          await this.userProductRepository.save(product);
          this.logger.log(`Updated product ${productId} with detail_id: ${newAdvancedInfo.id} during updateAdvancedInfo`);
        }

        return newAdvancedInfo;
      }

      // Cập nhật thông tin chung
      if (advancedInfoData.purchaseCount !== undefined) {
        advancedInfo.purchaseCount = advancedInfoData.purchaseCount;
      }

      // Cập nhật theo từng loại sản phẩm
      switch (productType) {
        case ProductTypeEnum.DIGITAL:
          if (advancedInfoData.digitalFulfillmentFlow !== undefined) {
            advancedInfo.digitalFulfillmentFlow = advancedInfoData.digitalFulfillmentFlow;
          }
          if (advancedInfoData.digitalOutput !== undefined) {
            advancedInfo.digitalOutput = advancedInfoData.digitalOutput;
          }
          break;

        case ProductTypeEnum.EVENT:
          if (advancedInfoData.eventFormat !== undefined) {
            advancedInfo.eventFormat = advancedInfoData.eventFormat;
          }
          if (advancedInfoData.eventLink !== undefined) {
            advancedInfo.eventLink = advancedInfoData.eventLink;
          }
          if (advancedInfoData.eventLocation !== undefined) {
            advancedInfo.eventLocation = advancedInfoData.eventLocation;
          }
          if (advancedInfoData.startDate !== undefined) {
            advancedInfo.startDate = advancedInfoData.startDate;
          }
          if (advancedInfoData.endDate !== undefined) {
            advancedInfo.endDate = advancedInfoData.endDate;
          }
          if (advancedInfoData.timezone !== undefined) {
            advancedInfo.timezone = advancedInfoData.timezone;
          }
          if (advancedInfoData.ticketTypes !== undefined) {
            // Xử lý imageOperations cho ticket types trước khi lưu
            const processedTicketTypes = await this.processTicketTypesImageOperations(advancedInfoData.ticketTypes);
            advancedInfo.ticketTypes = processedTicketTypes;
          }
          break;

        case ProductTypeEnum.SERVICE:
          if (advancedInfoData.servicePackages !== undefined) {
            // Xử lý imageOperations cho service packages trước khi lưu
            const processedServicePackages = await this.processServicePackagesImageOperations(
              advancedInfoData.servicePackages,
              advancedInfo.servicePackages || [] // Truyền service packages hiện có từ database
            );
            advancedInfo.servicePackages = processedServicePackages;
          }
          break;

        case ProductTypeEnum.COMBO:
          // Đối với COMBO, cập nhật purchaseCount và thông tin combo
          if (advancedInfoData.info !== undefined) {
            advancedInfo.combo = {
              info: advancedInfoData.info
            };
          }
          this.logger.log(`Cập nhật advanced info cho COMBO product với purchaseCount: ${advancedInfoData.purchaseCount} và ${advancedInfoData.info?.length || 0} sản phẩm`);
          break;

        default:
          throw new Error(`Loại sản phẩm ${productType} không hỗ trợ thông tin nâng cao`);
      }

      // Cập nhật thời gian
      advancedInfo.updatedAt = Date.now();

      const savedAdvancedInfo = await this.productAdvancedInfoRepository.save(advancedInfo);
      this.logger.log(`Cập nhật thông tin nâng cao thành công cho sản phẩm ${productId}`);

      return savedAdvancedInfo;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật thông tin nâng cao cho sản phẩm ${productId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tạo presigned URLs cho advanced images (ticket types, service packages)
   * @param advancedInfoData Dữ liệu advanced info
   * @param productType Loại sản phẩm
   * @param timestamp Timestamp để tạo tên file unique
   * @returns Danh sách presigned URLs cho advanced images
   */
  private async createAdvancedImagesUploadUrls(
    advancedInfoData: any,
    productType: ProductTypeEnum,
    timestamp: number
  ): Promise<Array<{ url: string; key: string; type: string; index: number; position: number }>> {
    const uploadUrls: Array<{ url: string; key: string; type: string; index: number; position: number }> = [];

    try {
      if (productType === ProductTypeEnum.EVENT && advancedInfoData.ticketTypes) {
        // Xử lý images cho ticket types
        for (let ticketIndex = 0; ticketIndex < advancedInfoData.ticketTypes.length; ticketIndex++) {
          const ticket = advancedInfoData.ticketTypes[ticketIndex];

          // Ưu tiên xử lý imageOperations (mới) hoặc images operations
          const imageOperations = ticket.imageOperations || ticket.images;
          if (imageOperations && Array.isArray(imageOperations)) {
            const addOperations = imageOperations.filter((img: any) => img.operation === 'ADD');
            for (let imageIndex = 0; imageIndex < addOperations.length; imageIndex++) {
              const addOp = addOperations[imageIndex];
              if (addOp.mimeType) {
                const mediaType = addOp.mimeType as ImageTypeEnum;
                const fileName = `ticket-${ticketIndex}-image-${imageIndex}-${timestamp}`;

                const imageUploadUrl = await this.createImageUploadUrl(fileName, mediaType);

                uploadUrls.push({
                  url: imageUploadUrl.url,
                  key: imageUploadUrl.key,
                  type: 'ticket',
                  index: ticketIndex,
                  position: imageIndex
                });
              }
            }
          }
          // Fallback: xử lý imagesMediaTypes (cũ) để backward compatibility
          else if (ticket.imagesMediaTypes && Array.isArray(ticket.imagesMediaTypes)) {
            for (let imageIndex = 0; imageIndex < ticket.imagesMediaTypes.length; imageIndex++) {
              const mediaType = ticket.imagesMediaTypes[imageIndex] as ImageTypeEnum;
              const fileName = `ticket-${ticketIndex}-image-${imageIndex}-${timestamp}`;

              const imageUploadUrl = await this.createImageUploadUrl(fileName, mediaType);

              uploadUrls.push({
                url: imageUploadUrl.url,
                key: imageUploadUrl.key,
                type: 'ticket',
                index: ticketIndex,
                position: imageIndex
              });
            }
          }
        }
      }

      if (productType === ProductTypeEnum.SERVICE && advancedInfoData.servicePackages) {
        // Xử lý images cho service packages
        for (let packageIndex = 0; packageIndex < advancedInfoData.servicePackages.length; packageIndex++) {
          const pkg = advancedInfoData.servicePackages[packageIndex];

          // Ưu tiên xử lý imageOperations (mới) hoặc images operations
          const imageOperations = pkg.imageOperations || pkg.images;
          if (imageOperations && Array.isArray(imageOperations)) {
            this.logger.log(`Xử lý imageOperations cho service package ${packageIndex}: ${imageOperations.length} operations`);

            const addOperations = imageOperations.filter((img: any) => img.operation === 'ADD');
            for (let imageIndex = 0; imageIndex < addOperations.length; imageIndex++) {
              const addOp = addOperations[imageIndex];
              if (addOp.mimeType) {
                const mediaType = addOp.mimeType as ImageTypeEnum;
                const fileName = `service-${packageIndex}-image-${imageIndex}-${timestamp}`;

                const imageUploadUrl = await this.createImageUploadUrl(fileName, mediaType);

                uploadUrls.push({
                  url: imageUploadUrl.url,
                  key: imageUploadUrl.key,
                  type: 'service',
                  index: packageIndex,
                  position: imageIndex
                });

                this.logger.log(`Tạo upload URL cho service package ${packageIndex} image ${imageIndex}: ${imageUploadUrl.key}`);
              }
            }
          }
          // Fallback: xử lý imagesMediaTypes (cũ) để backward compatibility
          else if (pkg.imagesMediaTypes && Array.isArray(pkg.imagesMediaTypes)) {
            for (let imageIndex = 0; imageIndex < pkg.imagesMediaTypes.length; imageIndex++) {
              const mediaType = pkg.imagesMediaTypes[imageIndex] as ImageTypeEnum;
              const fileName = `service-${packageIndex}-image-${imageIndex}-${timestamp}`;

              const imageUploadUrl = await this.createImageUploadUrl(fileName, mediaType);

              uploadUrls.push({
                url: imageUploadUrl.url,
                key: imageUploadUrl.key,
                type: 'service',
                index: packageIndex,
                position: imageIndex
              });
            }
          }
        }
      }

      if (productType === ProductTypeEnum.DIGITAL && advancedInfoData.variantMetadata?.variants) {
        // Xử lý images cho digital product variants
        for (let variantIndex = 0; variantIndex < advancedInfoData.variantMetadata.variants.length; variantIndex++) {
          const variant = advancedInfoData.variantMetadata.variants[variantIndex];

          // Ưu tiên xử lý imageOperations (mới) hoặc images operations
          const imageOperations = variant.imageOperations || variant.images;
          if (imageOperations && Array.isArray(imageOperations)) {
            const addOperations = imageOperations.filter((img: any) => img.operation === 'ADD');
            for (let imageIndex = 0; imageIndex < addOperations.length; imageIndex++) {
              const addOp = addOperations[imageIndex];
              if (addOp.mimeType) {
                const mediaType = addOp.mimeType as ImageTypeEnum;
                const fileName = `variant-${variantIndex}-image-${imageIndex}-${timestamp}`;

                const imageUploadUrl = await this.createImageUploadUrl(fileName, mediaType);

                uploadUrls.push({
                  url: imageUploadUrl.url,
                  key: imageUploadUrl.key,
                  type: 'variant',
                  index: variantIndex,
                  position: imageIndex
                });
              }
            }
          }
          // Fallback: xử lý imagesMediaTypes (cũ) để backward compatibility
          else if (variant.imagesMediaTypes && Array.isArray(variant.imagesMediaTypes)) {
            for (let imageIndex = 0; imageIndex < variant.imagesMediaTypes.length; imageIndex++) {
              const mediaType = variant.imagesMediaTypes[imageIndex] as ImageTypeEnum;
              const fileName = `variant-${variantIndex}-image-${imageIndex}-${timestamp}`;

              const imageUploadUrl = await this.createImageUploadUrl(fileName, mediaType);

              uploadUrls.push({
                url: imageUploadUrl.url,
                key: imageUploadUrl.key,
                type: 'variant',
                index: variantIndex,
                position: imageIndex
              });
            }
          }
        }
      }

      this.logger.log(`Tạo ${uploadUrls.length} presigned URLs cho advanced images`);
      return uploadUrls;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo presigned URLs cho advanced images: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Cập nhật variant metadata với S3 keys của ảnh đã tạo
   * @param product Sản phẩm cần cập nhật
   * @param advancedImagesUploadUrls Danh sách upload URLs với keys
   */
  private async updateVariantMetadataWithImageKeys(
    product: any,
    advancedImagesUploadUrls: Array<{ url: string; key: string; type: string; index: number; position: number }>
  ): Promise<void> {
    try {
      if (!product.metadata?.variants || !Array.isArray(product.metadata.variants)) {
        return;
      }

      // Lọc ra các variant images
      const variantImages = advancedImagesUploadUrls.filter(uploadUrl => uploadUrl.type === 'variant');

      if (variantImages.length === 0) {
        return;
      }

      // Cập nhật metadata variants với S3 keys
      const updatedVariants = [...product.metadata.variants];

      variantImages.forEach(imageUpload => {
        const variantIndex = imageUpload.index;
        const imagePosition = imageUpload.position;

        if (variantIndex >= 0 && variantIndex < updatedVariants.length) {
          // Khởi tạo images array nếu chưa có
          if (!updatedVariants[variantIndex].images) {
            updatedVariants[variantIndex].images = [];
          }

          // Thay thế placeholder key bằng S3 key thực tế
          const placeholderKey = `variant-${variantIndex}-image-${imagePosition}-`;
          const images = updatedVariants[variantIndex].images;

          // Tìm và thay thế placeholder key
          for (let i = 0; i < images.length; i++) {
            if (images[i].includes(placeholderKey)) {
              images[i] = imageUpload.key;
              break;
            }
          }

          // Nếu không tìm thấy placeholder, thêm key mới
          if (!images.includes(imageUpload.key)) {
            images.push(imageUpload.key);
          }
        }
      });

      // Cập nhật metadata
      product.metadata = {
        ...product.metadata,
        variants: updatedVariants
      };

      // Lưu sản phẩm với metadata đã cập nhật
      await this.userProductRepository.save(product);

      this.logger.log(`Cập nhật variant metadata với ${variantImages.length} S3 keys cho sản phẩm ${product.id}`);
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật variant metadata với image keys: ${error.message}`, error.stack);
    }
  }

  /**
   * Cập nhật advanced info với S3 keys của ảnh đã tạo cho EVENT và SERVICE products
   * @param advancedInfoId ID của advanced info
   * @param productType Loại sản phẩm
   * @param advancedImagesUploadUrls Danh sách upload URLs với keys
   */
  private async updateAdvancedInfoWithImageKeys(
    advancedInfoId: number,
    productType: ProductTypeEnum,
    advancedImagesUploadUrls: Array<{ url: string; key: string; type: string; index: number; position: number }>
  ): Promise<void> {
    try {
      this.logger.log(`updateAdvancedInfoWithImageKeys: advancedInfoId=${advancedInfoId}, productType=${productType}, uploadUrls=${JSON.stringify(advancedImagesUploadUrls)}`);

      // Lấy advanced info hiện tại
      const advancedInfo = await this.productAdvancedInfoRepository.findOne({
        where: { id: advancedInfoId }
      });
      if (!advancedInfo) {
        this.logger.warn(`Không tìm thấy advanced info với ID ${advancedInfoId}`);
        return;
      }

      this.logger.log(`Found advanced info: ticketTypes=${advancedInfo.ticketTypes ? advancedInfo.ticketTypes.length : 'null'}, servicePackages=${advancedInfo.servicePackages ? advancedInfo.servicePackages.length : 'null'}`);

      let updated = false;

      if (productType === ProductTypeEnum.EVENT && advancedInfo.ticketTypes) {
        // Lọc ra các ticket images (type có thể là 'ticket' hoặc 'ticketType')
        const ticketImages = advancedImagesUploadUrls.filter(uploadUrl =>
          uploadUrl.type === 'ticket' || uploadUrl.type === 'ticketType'
        );

        if (ticketImages.length > 0) {
          // Tạo deep copy để tránh reference issues với JSONB
          const updatedTicketTypes = JSON.parse(JSON.stringify(advancedInfo.ticketTypes)) as any[];

          ticketImages.forEach(imageUpload => {
            const ticketIndex = imageUpload.index;
            const imagePosition = imageUpload.position;

            this.logger.log(`Processing ticket image: ticketIndex=${ticketIndex}, position=${imagePosition}, key=${imageUpload.key}`);

            if (ticketIndex >= 0 && ticketIndex < updatedTicketTypes.length) {
              // Khởi tạo images array nếu chưa có
              if (!updatedTicketTypes[ticketIndex].images) {
                updatedTicketTypes[ticketIndex].images = [];
              }

              // Thêm S3 key mới cho ADD operations
              updatedTicketTypes[ticketIndex].images.push({
                key: imageUpload.key,
                position: imagePosition
              });

              this.logger.log(`Added image to ticket ${ticketIndex}: ${JSON.stringify(updatedTicketTypes[ticketIndex].images)}`);
            }
          });

          // Cập nhật với deep copy
          advancedInfo.ticketTypes = updatedTicketTypes;
          updated = true;
          this.logger.log(`Updated ticketTypes: ${JSON.stringify(advancedInfo.ticketTypes)}`);
        }
      }

      if (productType === ProductTypeEnum.SERVICE && advancedInfo.servicePackages) {
        // Lọc ra các service package images (type có thể là 'service' hoặc 'servicePackage')
        const serviceImages = advancedImagesUploadUrls.filter(uploadUrl =>
          uploadUrl.type === 'service' || uploadUrl.type === 'servicePackage'
        );

        if (serviceImages.length > 0) {
          // Tạo deep copy để tránh reference issues với JSONB
          const updatedServicePackages = JSON.parse(JSON.stringify(advancedInfo.servicePackages)) as any[];

          serviceImages.forEach(imageUpload => {
            const packageIndex = imageUpload.index;
            const imagePosition = imageUpload.position;

            this.logger.log(`Processing service image: packageIndex=${packageIndex}, position=${imagePosition}, key=${imageUpload.key}`);

            if (packageIndex >= 0 && packageIndex < updatedServicePackages.length) {
              // Khởi tạo images array nếu chưa có
              if (!updatedServicePackages[packageIndex].images) {
                updatedServicePackages[packageIndex].images = [];
              }

              // Thêm S3 key mới cho ADD operations
              updatedServicePackages[packageIndex].images.push({
                key: imageUpload.key,
                position: imagePosition
              });

              this.logger.log(`Added image to service package ${packageIndex}: ${JSON.stringify(updatedServicePackages[packageIndex].images)}`);
            }
          });

          // Cập nhật với deep copy
          advancedInfo.servicePackages = updatedServicePackages;
          updated = true;
          this.logger.log(`Updated servicePackages: ${JSON.stringify(advancedInfo.servicePackages)}`);
        }
      }

      if (updated) {
        advancedInfo.updatedAt = Date.now();

        // Log trước khi save
        this.logger.log(`Saving advanced info with updated data: ticketTypes=${JSON.stringify(advancedInfo.ticketTypes)}`);

        const savedAdvancedInfo = await this.productAdvancedInfoRepository.save(advancedInfo);

        // Log sau khi save để verify
        this.logger.log(`Saved advanced info result: ticketTypes=${JSON.stringify(savedAdvancedInfo.ticketTypes)}`);
        this.logger.log(`Cập nhật advanced info với ${advancedImagesUploadUrls.length} S3 keys cho ${productType} product`);
      } else {
        this.logger.log(`No updates needed for advanced info`);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật advanced info với image keys: ${error.message}`, error.stack);
      throw error; // Re-throw để có thể debug
    }
  }

  /**
   * Xử lý imageOperations cho advanced images (ticket types/service packages)
   * Theo đúng logic như sản phẩm vật lý/số: DELETE trước, ADD sau
   * @param imageOperations Danh sách operations
   * @param advancedInfo Advanced info entity
   * @param productType Loại sản phẩm
   * @param advancedImagesUploadUrls Mảng chứa upload URLs
   * @param timestamp Timestamp
   */
  private async processAdvancedImageOperations(
    imageOperations: any[],
    advancedInfo: ProductAdvancedInfo,
    productType: ProductTypeEnum,
    advancedImagesUploadUrls: Array<{ url: string; key: string; type: string; index: number; position: number }>,
    timestamp: number
  ): Promise<void> {
    // Validate operations trước khi thực hiện
    for (const operation of imageOperations) {
      if (operation.operation === 'DELETE' && !operation.key) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Key là bắt buộc khi operation = DELETE cho advanced images'
        );
      }
      if (operation.operation === 'ADD' && !operation.mimeType) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'MIME type là bắt buộc khi operation = ADD cho advanced images'
        );
      }
    }

    // Tạo deep copy để tránh reference issues với JSONB
    let updatedData: any = {};
    let hasChanges = false;

    if (productType === ProductTypeEnum.EVENT && advancedInfo.ticketTypes) {
      updatedData.ticketTypes = JSON.parse(JSON.stringify(advancedInfo.ticketTypes));
    } else if (productType === ProductTypeEnum.SERVICE && advancedInfo.servicePackages) {
      updatedData.servicePackages = JSON.parse(JSON.stringify(advancedInfo.servicePackages));
    }

    // Bước 1: Xử lý DELETE operations trước (như logic sản phẩm vật lý/số)
    const deleteOperations = imageOperations.filter(op => op.operation === 'DELETE');
    for (const deleteOp of deleteOperations) {
      this.logger.log(`Xử lý DELETE operation cho key: ${deleteOp.key}`);

      if (productType === ProductTypeEnum.EVENT && updatedData.ticketTypes) {
        for (let ticketIndex = 0; ticketIndex < updatedData.ticketTypes.length; ticketIndex++) {
          const ticket = updatedData.ticketTypes[ticketIndex];
          if (ticket.images && Array.isArray(ticket.images)) {
            const imageToDelete = ticket.images.find((img: any) => img.key === deleteOp.key);
            if (imageToDelete) {
              try {
                // Xóa file trên S3
                await this.s3Service.deleteFile(deleteOp.key);
                // Xóa khỏi array
                ticket.images = ticket.images.filter((img: any) => img.key !== deleteOp.key);
                hasChanges = true;
                this.logger.log(`Đã xóa image ${deleteOp.key} từ ticket ${ticketIndex}`);
                break;
              } catch (deleteError) {
                this.logger.warn(`Không thể xóa image ${deleteOp.key}: ${deleteError.message}`);
              }
            }
          }
        }
      } else if (productType === ProductTypeEnum.SERVICE && updatedData.servicePackages) {
        for (let packageIndex = 0; packageIndex < updatedData.servicePackages.length; packageIndex++) {
          const pkg = updatedData.servicePackages[packageIndex];
          if (pkg.images && Array.isArray(pkg.images)) {
            const imageToDelete = pkg.images.find((img: any) => img.key === deleteOp.key);
            if (imageToDelete) {
              try {
                // Xóa file trên S3
                await this.s3Service.deleteFile(deleteOp.key);
                // Xóa khỏi array
                pkg.images = pkg.images.filter((img: any) => img.key !== deleteOp.key);
                hasChanges = true;
                this.logger.log(`Đã xóa image ${deleteOp.key} từ service package ${packageIndex}`);
                break;
              } catch (deleteError) {
                this.logger.warn(`Không thể xóa image ${deleteOp.key}: ${deleteError.message}`);
              }
            }
          }
        }
      }
    }

    // Bước 2: Xử lý ADD operations sau (như logic sản phẩm vật lý/số)
    const addOperations = imageOperations.filter(op => op.operation === 'ADD');
    for (let i = 0; i < addOperations.length; i++) {
      const addOp = addOperations[i];
      const mediaType = addOp.mimeType as ImageTypeEnum;
      const fileName = `${productType.toLowerCase()}-image-${i}-${timestamp}`;

      const imageUploadUrl = await this.createImageUploadUrl(fileName, mediaType);

      // Xác định target index (giả sử thêm vào item đầu tiên, có thể mở rộng logic sau)
      const targetIndex = addOp.targetIndex || 0;

      if (productType === ProductTypeEnum.EVENT && updatedData.ticketTypes) {
        if (targetIndex >= 0 && targetIndex < updatedData.ticketTypes.length) {
          // Khởi tạo images array nếu chưa có
          if (!updatedData.ticketTypes[targetIndex].images) {
            updatedData.ticketTypes[targetIndex].images = [];
          }

          // Tính position mới
          const currentImages = updatedData.ticketTypes[targetIndex].images;
          const maxPosition = currentImages.length > 0
            ? Math.max(...currentImages.map((img: any) => img.position || 0))
            : -1;
          const newPosition = addOp.position !== undefined ? addOp.position : maxPosition + 1;

          // Thêm image mới
          updatedData.ticketTypes[targetIndex].images.push({
            key: imageUploadUrl.key,
            position: newPosition
          });

          // Thêm vào upload URLs
          advancedImagesUploadUrls.push({
            url: imageUploadUrl.url,
            key: imageUploadUrl.key,
            type: 'ticket',
            index: targetIndex,
            position: newPosition
          });

          hasChanges = true;
          this.logger.log(`Thêm image mới ${imageUploadUrl.key} vào ticket ${targetIndex} position ${newPosition}`);
        }
      } else if (productType === ProductTypeEnum.SERVICE && updatedData.servicePackages) {
        if (targetIndex >= 0 && targetIndex < updatedData.servicePackages.length) {
          // Khởi tạo images array nếu chưa có
          if (!updatedData.servicePackages[targetIndex].images) {
            updatedData.servicePackages[targetIndex].images = [];
          }

          // Tính position mới
          const currentImages = updatedData.servicePackages[targetIndex].images;
          const maxPosition = currentImages.length > 0
            ? Math.max(...currentImages.map((img: any) => img.position || 0))
            : -1;
          const newPosition = addOp.position !== undefined ? addOp.position : maxPosition + 1;

          // Thêm image mới
          updatedData.servicePackages[targetIndex].images.push({
            key: imageUploadUrl.key,
            position: newPosition
          });

          // Thêm vào upload URLs
          advancedImagesUploadUrls.push({
            url: imageUploadUrl.url,
            key: imageUploadUrl.key,
            type: 'service',
            index: targetIndex,
            position: newPosition
          });

          hasChanges = true;
          this.logger.log(`Thêm image mới ${imageUploadUrl.key} vào service package ${targetIndex} position ${newPosition}`);
        }
      }
    }

    // Bước 3: Lưu changes vào database nếu có thay đổi
    if (hasChanges) {
      if (productType === ProductTypeEnum.EVENT && updatedData.ticketTypes) {
        advancedInfo.ticketTypes = updatedData.ticketTypes;
      } else if (productType === ProductTypeEnum.SERVICE && updatedData.servicePackages) {
        advancedInfo.servicePackages = updatedData.servicePackages;
      }

      advancedInfo.updatedAt = Date.now();
      await this.productAdvancedInfoRepository.save(advancedInfo);

      this.logger.log(`Đã lưu ${imageOperations.length} image operations cho ${productType} product`);
    }
  }

  /**
   * Xử lý imageOperations cho ticket types (theo pattern của sản phẩm số)
   * @param ticketTypes Danh sách ticket types với imageOperations
   * @returns Ticket types đã xử lý DELETE operations
   */
  private async processTicketTypesImageOperations(ticketTypes: any[]): Promise<any[]> {
    if (!ticketTypes || !Array.isArray(ticketTypes)) {
      return [];
    }

    const processedTicketTypes: any[] = [];

    for (let ticketIndex = 0; ticketIndex < ticketTypes.length; ticketIndex++) {
      const ticket = { ...ticketTypes[ticketIndex] };

      // Xử lý imageOperations nếu có
      const imageOperations = ticket.imageOperations || ticket.images;
      if (imageOperations && Array.isArray(imageOperations)) {
        this.logger.log(`Xử lý imageOperations cho ticket type ${ticketIndex}: ${imageOperations.length} operations`);

        // Lấy images hiện có
        let currentImages = ticket.images || [];

        // Xử lý DELETE operations trước
        const deleteOperations = imageOperations.filter((op: any) => op.operation === 'DELETE');
        for (const deleteOp of deleteOperations) {
          if (deleteOp.key) {
            try {
              // Xóa file trên S3
              await this.s3Service.deleteFile(deleteOp.key);
              // Xóa khỏi array
              currentImages = currentImages.filter((img: any) => img.key !== deleteOp.key);
              this.logger.log(`Đã xóa image ${deleteOp.key} từ ticket type ${ticketIndex}`);
            } catch (deleteError) {
              this.logger.warn(`Không thể xóa image ${deleteOp.key}: ${deleteError.message}`);
            }
          }
        }

        // Cập nhật images sau khi xóa
        ticket.images = currentImages;

        // Xóa imageOperations khỏi ticket để không lưu vào database
        delete ticket.imageOperations;
      }

      processedTicketTypes.push(ticket);
    }

    return processedTicketTypes;
  }

  /**
   * Xử lý imageOperations cho service packages (theo pattern của sản phẩm số)
   * @param servicePackages Danh sách service packages với imageOperations
   * @returns Service packages đã xử lý DELETE operations
   */
  private async processServicePackagesImageOperations(
    servicePackages: any[],
    existingServicePackages: any[] = []
  ): Promise<any[]> {
    if (!servicePackages || !Array.isArray(servicePackages)) {
      return [];
    }

    const processedServicePackages: any[] = [];

    for (let packageIndex = 0; packageIndex < servicePackages.length; packageIndex++) {
      const pkg = { ...servicePackages[packageIndex] };

      // Lấy images hiện có từ database (từ existing service packages)
      const existingPkg = existingServicePackages.find(existing => existing.id === pkg.id);
      let currentImages = existingPkg?.images || pkg.images || [];

      this.logger.log(`Service package ${packageIndex} (id: ${pkg.id}): images hiện có = ${currentImages.length}`);

      // Xử lý imageOperations nếu có
      const imageOperations = pkg.imageOperations || pkg.images;
      if (imageOperations && Array.isArray(imageOperations)) {
        this.logger.log(`Xử lý imageOperations cho service package ${packageIndex}: ${imageOperations.length} operations`);

        // CHỈ xử lý DELETE operations nếu thực sự có DELETE operations
        const deleteOperations = imageOperations.filter((op: any) => op.operation === 'DELETE');
        if (deleteOperations.length > 0) {
          this.logger.log(`Xử lý ${deleteOperations.length} DELETE operations cho service package ${packageIndex}`);

          for (const deleteOp of deleteOperations) {
            if (deleteOp.key) {
              try {
                // Xóa file trên S3
                await this.s3Service.deleteFile(deleteOp.key);
                // Xóa khỏi array
                currentImages = currentImages.filter((img: any) => img.key !== deleteOp.key);
                this.logger.log(`Đã xóa image ${deleteOp.key} từ service package ${packageIndex}`);
              } catch (deleteError) {
                this.logger.warn(`Không thể xóa image ${deleteOp.key}: ${deleteError.message}`);
              }
            }
          }
        } else {
          this.logger.log(`Không có DELETE operations cho service package ${packageIndex}, giữ nguyên ${currentImages.length} images hiện có`);
        }

        // Xóa imageOperations khỏi package để không lưu vào database
        delete pkg.imageOperations;
      }

      // Cập nhật images (giữ lại images hiện có sau khi xử lý DELETE)
      pkg.images = currentImages;

      processedServicePackages.push(pkg);
    }

    return processedServicePackages;
  }

  /**
   * Xử lý ticket types với images để trả về response
   * @param ticketTypes Danh sách ticket types từ database
   * @returns Ticket types với images đã được xử lý với URLs
   */
  private async processTicketTypesWithImages(ticketTypes: any[]): Promise<any[]> {
    if (!ticketTypes || !Array.isArray(ticketTypes)) {
      return [];
    }

    return Promise.all(
      ticketTypes.map(async (ticket) => {
        const processedTicket = { ...ticket };

        // Xử lý images nếu có
        if (ticket.images && Array.isArray(ticket.images)) {
          processedTicket.images = await Promise.all(
            ticket.images.map(async (img: any) => {
              try {
                // Tạo CDN URL cho image
                const url = this.cdnService.generateUrlView(img.key, TimeIntervalEnum.ONE_HOUR);
                const timestamp = Date.now();

                return {
                  key: img.key,
                  position: img.position || 0,
                  url: url ? `${url}?t=${timestamp}` : ''
                };
              } catch (error) {
                this.logger.error(`Lỗi khi tạo URL cho ticket image: ${error.message}`, error.stack);
                return {
                  key: img.key || '',
                  position: img.position || 0,
                  url: ''
                };
              }
            })
          );
        }

        return processedTicket;
      })
    );
  }

  /**
   * Xử lý service packages với images để trả về response
   * @param servicePackages Danh sách service packages từ database
   * @returns Service packages với images đã được xử lý với URLs
   */
  private async processServicePackagesWithImages(servicePackages: any[]): Promise<any[]> {
    if (!servicePackages || !Array.isArray(servicePackages)) {
      return [];
    }

    return Promise.all(
      servicePackages.map(async (pkg) => {
        const processedPackage = { ...pkg };

        // Xử lý images nếu có
        if (pkg.images && Array.isArray(pkg.images)) {
          processedPackage.images = await Promise.all(
            pkg.images.map(async (img: any) => {
              try {
                // Tạo CDN URL cho image
                const url = this.cdnService.generateUrlView(img.key, TimeIntervalEnum.ONE_HOUR);
                const timestamp = Date.now();

                return {
                  key: img.key,
                  position: img.position || 0,
                  url: url ? `${url}?t=${timestamp}` : ''
                };
              } catch (error) {
                this.logger.error(`Lỗi khi tạo URL cho service package image: ${error.message}`, error.stack);
                return {
                  key: img.key || '',
                  position: img.position || 0,
                  url: ''
                };
              }
            })
          );
        }

        return processedPackage;
      })
    );
  }



  /**
   * Xử lý cập nhật hình ảnh theo 3 cách: imagesMediaTypes, imageOperations, hoặc images (deprecated)
   * @param updateProductDto DTO cập nhật sản phẩm
   * @param product Entity sản phẩm
   * @param imagesUploadUrls Mảng chứa URLs upload
   * @param now Timestamp hiện tại
   */
  private async processImageUpdates(
    updateProductDto: BusinessUpdateProductDto,
    product: UserProduct,
    imagesUploadUrls: Array<{ url: string; key: string; index: number }>,
    now: number
  ): Promise<void> {
    try {
      // Trường hợp 1: Sử dụng imagesMediaTypes (thay thế toàn bộ hình ảnh)
      if (updateProductDto.imagesMediaTypes && updateProductDto.imagesMediaTypes.length > 0) {
        this.logger.log(`Xử lý imagesMediaTypes: thay thế toàn bộ ${product.images.length} hình ảnh hiện tại bằng ${updateProductDto.imagesMediaTypes.length} hình ảnh mới`);

        // Xóa tất cả hình ảnh hiện tại trên S3
        for (const existingImage of product.images) {
          try {
            await this.s3Service.deleteFile(existingImage.key);
            this.logger.debug(`Đã xóa hình ảnh cũ: ${existingImage.key}`);
          } catch (deleteError) {
            this.logger.warn(`Không thể xóa hình ảnh cũ ${existingImage.key}: ${deleteError.message}`);
          }
        }

        // Xóa tất cả hình ảnh khỏi product
        product.images = [];

        // Tạo hình ảnh mới
        for (let i = 0; i < updateProductDto.imagesMediaTypes.length; i++) {
          const mediaType = updateProductDto.imagesMediaTypes[i] as ImageTypeEnum;
          const fileName = `product-image-${i}-${now}`;

          const imageUploadUrl = await this.createImageUploadUrl(fileName, mediaType);

          // Thêm vào danh sách ảnh của sản phẩm
          product.images.push({
            key: imageUploadUrl.key,
            position: i
          });

          // Thêm vào danh sách URL upload
          imagesUploadUrls.push({
            url: imageUploadUrl.url,
            key: imageUploadUrl.key,
            index: i
          });

          this.logger.debug(`Tạo hình ảnh mới: ${imageUploadUrl.key} tại vị trí ${i}`);
        }

        return; // Kết thúc xử lý
      }

      // Trường hợp 2: Sử dụng imageOperations (thao tác chi tiết)
      // Logic mới: imageOperations ở level root LUÔN dành cho PRODUCT images
      // imageOperations cho advanced images sẽ nằm trong advancedInfo.servicePackages/ticketTypes
      if (updateProductDto.imageOperations && updateProductDto.imageOperations.length > 0) {
        this.logger.log(`Xử lý imageOperations cho PRODUCT images: ${updateProductDto.imageOperations.length} thao tác`);

        await this.processImageOperations(updateProductDto.imageOperations, product, imagesUploadUrls, now);
        return; // Kết thúc xử lý
      }

      // Trường hợp 3: Sử dụng images (deprecated - để tương thích ngược)
      if (updateProductDto.images && updateProductDto.images.length > 0) {
        // Kiểm tra xem có phải images cho product không
        const isProductImages = (updateProductDto as any).isProductImageOperations === true ||
                               product.productType === ProductTypeEnum.PHYSICAL ||
                               !product.detail_id;

        if (isProductImages) {
          this.logger.log(`Xử lý images cho PRODUCT: ${updateProductDto.images.length} thao tác`);

          await this.processLegacyImageOperations(updateProductDto.images, product, imagesUploadUrls, now);
          return; // Kết thúc xử lý
        } else {
          this.logger.log(`images sẽ được xử lý cho ADVANCED images (${product.productType})`);
          // Không return ở đây, để logic advanced images xử lý sau
        }
      }

      // Trường hợp 4: Không có thay đổi hình ảnh
      this.logger.debug('Không có thay đổi hình ảnh nào');

    } catch (error) {
      this.logger.error(`Lỗi khi xử lý cập nhật hình ảnh: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_UPDATE_FAILED,
        `Lỗi khi xử lý hình ảnh: ${error.message}`
      );
    }
  }

  /**
   * Xử lý các thao tác hình ảnh với imageOperations
   */
  private async processImageOperations(
    imageOperations: any[],
    product: UserProduct,
    imagesUploadUrls: Array<{ url: string; key: string; index: number }>,
    now: number
  ): Promise<void> {
    // Validate operations trước khi thực hiện
    for (const operation of imageOperations) {
      if (operation.operation === 'DELETE' && !operation.key) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Key là bắt buộc khi operation = DELETE'
        );
      }
      if (operation.operation === 'ADD' && !operation.mimeType) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'MIME type là bắt buộc khi operation = ADD'
        );
      }
    }

    // Xử lý các thao tác DELETE trước
    const deleteOperations = imageOperations.filter(op => op.operation === 'DELETE');
    for (const deleteOp of deleteOperations) {
      const imageToDelete = product.images.find((img: any) => img.key === deleteOp.key);
      if (imageToDelete) {
        try {
          await this.s3Service.deleteFile(imageToDelete.key);
          product.images = product.images.filter((img: any) => img.key !== deleteOp.key);
          this.logger.debug(`Đã xóa hình ảnh: ${deleteOp.key}`);
        } catch (deleteError) {
          this.logger.warn(`Không thể xóa hình ảnh ${deleteOp.key}: ${deleteError.message}`);
        }
      } else {
        this.logger.warn(`Không tìm thấy hình ảnh với key: ${deleteOp.key}`);
      }
    }

    // Xử lý các thao tác ADD
    const addOperations = imageOperations.filter(op => op.operation === 'ADD');
    for (const addOp of addOperations) {
      const maxPosition = product.images.length > 0
        ? Math.max(...product.images.map((img: any) => img.position))
        : -1;
      const newPosition = addOp.position !== undefined ? addOp.position : maxPosition + 1;

      const mediaType = addOp.mimeType as ImageTypeEnum;
      const fileName = `product-image-${newPosition}-${now}`;

      const imageUploadUrl = await this.createImageUploadUrl(fileName, mediaType);

      product.images.push({
        key: imageUploadUrl.key,
        position: newPosition
      });

      imagesUploadUrls.push({
        url: imageUploadUrl.url,
        key: imageUploadUrl.key,
        index: newPosition
      });

      this.logger.debug(`Thêm hình ảnh mới: ${imageUploadUrl.key} tại vị trí ${newPosition}`);
    }
  }

  /**
   * Xử lý các thao tác hình ảnh với images (deprecated)
   */
  private async processLegacyImageOperations(
    images: any[],
    product: UserProduct,
    imagesUploadUrls: Array<{ url: string; key: string; index: number }>,
    now: number
  ): Promise<void> {
    // Xử lý các thao tác DELETE trước
    const deleteOperations = images.filter((img: any) => img.operation === 'DELETE');
    for (const deleteOp of deleteOperations) {
      // Xóa theo key nếu có
      if (deleteOp.key) {
        const imageToDelete = product.images.find((img: any) => img.key === deleteOp.key);
        if (imageToDelete) {
          await this.s3Service.deleteFile(imageToDelete.key);
          product.images = product.images.filter((img: any) => img.key !== deleteOp.key);
        }
      }
      // Xóa theo position nếu có
      else if (deleteOp.position !== undefined) {
        const imageToDelete = product.images.find((img: any) => img.position === deleteOp.position);
        if (imageToDelete) {
          await this.s3Service.deleteFile(imageToDelete.key);
          product.images = product.images.filter((img: any) => img.position !== deleteOp.position);
        }
      }
    }

    // Xử lý các thao tác ADD
    const addOperations = images.filter((img: any) => img.operation === 'ADD');
    for (const addOp of addOperations) {
      if (addOp.mimeType) {
        const maxPosition = product.images.length > 0
          ? Math.max(...product.images.map((img: any) => img.position))
          : -1;
        const newPosition = maxPosition + 1;

        const mediaType = addOp.mimeType as ImageTypeEnum;
        const fileName = `product-image-${newPosition}-${now}`;

        const imageUploadUrl = await this.createImageUploadUrl(fileName, mediaType);

        product.images.push({
          key: imageUploadUrl.key,
          position: newPosition
        });

        imagesUploadUrls.push({
          url: imageUploadUrl.url,
          key: imageUploadUrl.key,
          index: newPosition
        });
      }
    }
  }

  /**
   * Convert BusinessCreateProductDto sang CreatedProductDto type-safe
   */
  private convertToTypeSafeDto(dto: BusinessCreateProductDto): CreatedProductDto {
    const baseFields = {
      name: dto.name,
      description: dto.description,
      typePrice: dto.typePrice,
      price: dto.price,
      imagesMediaTypes: dto.imagesMediaTypes,
      tags: dto.tags,
      customFields: dto.customFields,
      shipmentConfig: dto.shipmentConfig,
      classifications: dto.classifications,
    };

    switch (dto.productType) {
      case ProductTypeEnum.PHYSICAL:
        return {
          ...baseFields,
          productType: ProductTypeEnum.PHYSICAL,
          inventory: dto.inventory,
        } as PhysicalProductCreateDto;

      case ProductTypeEnum.DIGITAL:
        return {
          ...baseFields,
          productType: ProductTypeEnum.DIGITAL,
          advancedInfo: dto.advancedInfo,
          purchaseCount: 0,
        } as DigitalProductCreateDto;

      case ProductTypeEnum.EVENT:
        return {
          ...baseFields,
          productType: ProductTypeEnum.EVENT,
          advancedInfo: dto.advancedInfo,
          purchaseCount: 0,
        } as EventProductCreateDto;

      case ProductTypeEnum.SERVICE:
        return {
          ...baseFields,
          productType: ProductTypeEnum.SERVICE,
          advancedInfo: dto.advancedInfo,
          purchaseCount: 0,
        } as ServiceProductCreateDto;

      case ProductTypeEnum.COMBO:
        return {
          ...baseFields,
          productType: ProductTypeEnum.COMBO,
          advancedInfo: dto.advancedInfo,
          purchaseCount: 0,
        } as ComboProductCreateDto;

      default:
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_PRODUCT_TYPE,
          `Cannot convert to type-safe DTO for product type: ${dto.productType}`,
        );
    }
  }
}