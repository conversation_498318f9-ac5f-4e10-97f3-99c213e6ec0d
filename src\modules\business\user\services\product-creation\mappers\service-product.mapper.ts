import { Injectable } from '@nestjs/common';
import { UserProduct } from '@modules/business/entities/user-product.entity';
import { ServiceProductResponseDto, ServiceAdvancedInfoResponseDto } from '../../../dto/response/service-product-response.dto';
import { ProductCreationResult } from '../handlers/base-product.handler';
import { BaseProductMapper } from './base-product.mapper';

/**
 * Mapper cho Service Product
 * Chuyển đổi UserProduct entity sang ServiceProductResponseDto
 */
@Injectable()
export class ServiceProductMapper extends BaseProductMapper<ServiceProductResponseDto> {
  /**
   * Map UserProduct entity sang ServiceProductResponseDto
   */
  async mapToResponseDto(
    product: UserProduct,
    result: ProductCreationResult
  ): Promise<ServiceProductResponseDto> {
    // Map common fields
    const commonFields = this.mapCommonFields(product, result);

    // Map service-specific fields
    const serviceResponse: ServiceProductResponseDto = {
      ...commonFields,
      productType: product.productType as any, // SERVICE
      advancedInfo: this.mapServiceAdvancedInfo(result.advancedInfo),
      shipmentConfig: {
        widthCm: 0,
        heightCm: 0,
        lengthCm: 0,
        weightGram: 0
      },
      // Service-specific fields from metadata
      serviceTime: product.metadata?.serviceTime,
      serviceDuration: product.metadata?.serviceDuration,
      serviceProvider: product.metadata?.serviceProvider,
      serviceType: product.metadata?.serviceType,
      serviceLocation: product.metadata?.serviceLocation
    } as ServiceProductResponseDto;

    // Transform using class-transformer
    return this.transform(ServiceProductResponseDto, serviceResponse);
  }

  /**
   * Map advanced info cho service product
   */
  private mapServiceAdvancedInfo(advancedInfo: any): ServiceAdvancedInfoResponseDto | undefined {
    if (!advancedInfo) {
      return undefined;
    }

    return {
      purchaseCount: advancedInfo.purchaseCount || 0,
      servicePackages: this.mapServicePackages(advancedInfo.servicePackages),
      images: advancedInfo.images || []
    };
  }

  /**
   * Map service packages
   */
  private mapServicePackages(servicePackages: any[]): any[] {
    if (!servicePackages || servicePackages.length === 0) {
      return [];
    }

    return servicePackages.map(servicePackage => ({
      id: servicePackage.id,
      name: servicePackage.name,
      price: servicePackage.price,
      startTime: servicePackage.startTime,
      endTime: servicePackage.endTime,
      timezone: servicePackage.timezone,
      description: servicePackage.description,
      quantity: servicePackage.quantity,
      minQuantityPerPurchase: servicePackage.minQuantityPerPurchase,
      maxQuantityPerPurchase: servicePackage.maxQuantityPerPurchase,
      status: servicePackage.status,
      images: servicePackage.images || [],
      durationHours: servicePackage.durationHours,
      features: servicePackage.features || [],
      isPopular: servicePackage.isPopular || false
    }));
  }
}
