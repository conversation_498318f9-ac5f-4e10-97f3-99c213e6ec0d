import { Injectable, Logger } from '@nestjs/common';
import {
  ProductAdvancedInfoRepository,
} from '@modules/business/repositories';
import { DigitalProductCreateDto } from '../../dto';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { UserProduct } from '@modules/business/entities';
import { ValidationHelper } from '../../helpers/validation.helper';
import { ProductValidationHelper } from '../../helpers/product-validation.helper';
import { ClassificationService } from '../classification.service';
import { CreateProductProcessor } from './create-product.processor';
import { CreateProductResult } from './create-product-orchestrator';
import { S3Service } from '@shared/services/s3.service';
import { CategoryFolderEnum, generateS3Key } from '@shared/utils/generators/s3-key-generator.util';
import { FileSizeEnum, ImageTypeEnum, TimeIntervalEnum } from '@shared/utils';

/**
 * Processor chuyên xử lý logic tạo sản phẩm số
 * Bóc tách từ UserProductService để tối ưu hóa và dễ maintain
 */
@Injectable()
export class DigitalProductProcessor {
  private readonly logger = new Logger(DigitalProductProcessor.name);

  constructor(
    private readonly createProcessor: CreateProductProcessor,
    private readonly productAdvancedInfoRepository: ProductAdvancedInfoRepository,
    private readonly validationHelper: ValidationHelper,
    private readonly productValidationHelper: ProductValidationHelper,
    private readonly classificationService: ClassificationService,
    private readonly s3Service: S3Service,
  ) {}

  /**
   * Tạo sản phẩm số hoàn chỉnh
   */
  async createDigitalProduct(
    dto: DigitalProductCreateDto,
    userId: number,
  ): Promise<CreateProductResult> {
    this.logger.log(`Creating DIGITAL product: ${dto.name}`);

    // BƯỚC 1: Validate dữ liệu đầu vào cho sản phẩm số
    await this.validateDigitalProductData(dto);

    // BƯỚC 2: Xử lý variant metadata và custom fields
    const variantMetadata = await this.processDigitalVariantMetadata(dto);
    const { customFields, metadata } = await this.createProcessor.processCustomFields(dto, variantMetadata);

    // BƯỚC 3: Tạo sản phẩm cơ bản với shipment config = 0
    const product = await this.createProcessor.createBaseProduct(dto, userId, metadata);
    product.shipmentConfig = { widthCm: 0, heightCm: 0, lengthCm: 0, weightGram: 0 };

    // BƯỚC 4: Lưu sản phẩm để có ID
    const savedProduct = await this.createProcessor.saveProduct(product);

    // BƯỚC 5: Xử lý hình ảnh sản phẩm chính
    const { imageEntries, imagesUploadUrls } = await this.createProcessor.processProductImages(dto, Date.now());

    // BƯỚC 6: Cập nhật sản phẩm với thông tin hình ảnh
    savedProduct.images = imageEntries;
    await this.createProcessor.saveProduct(savedProduct);

    // BƯỚC 7: Tạo advanced info (variants)
    const advancedInfo = await this.createAdvancedInfo(savedProduct.id, dto.productType, dto.advancedInfo, []);

    // BƯỚC 8: Xử lý hình ảnh cho variants
    const advancedImagesUploadUrls = await this.createAdvancedImagesUploadUrls(dto.advancedInfo, dto.productType, Date.now());

    // BƯỚC 9: Cập nhật detail_id để liên kết với advanced info
    if (advancedInfo?.id) {
      savedProduct.detail_id = advancedInfo.id;
      await this.createProcessor.saveProduct(savedProduct);

      // Cập nhật variant metadata với image keys nếu có hình ảnh variants
      if (advancedImagesUploadUrls.length > 0) {
        await this.updateVariantMetadataWithImageKeys(savedProduct, advancedImagesUploadUrls);
      }
    }

    // BƯỚC 10: Xử lý classifications
    const classifications = await this.processClassifications(savedProduct.id, dto.classifications || [], userId);

    // BƯỚC 11: Lấy sản phẩm cuối cùng
    const finalProduct = await this.createProcessor.getProductById(savedProduct.id);

    // BƯỚC 12: Tạo object chứa upload URLs cho cả sản phẩm chính và variants
    const uploadUrls: any = {
      productId: finalProduct.id.toString(),
      imagesUploadUrls: imagesUploadUrls || []
    };

    // Thêm upload URLs cho hình ảnh variants nếu có
    if (advancedImagesUploadUrls && advancedImagesUploadUrls.length > 0) {
      uploadUrls.advancedImagesUploadUrls = advancedImagesUploadUrls;
    }

    return {
      product: finalProduct,
      uploadUrls: (imagesUploadUrls.length > 0 || advancedImagesUploadUrls.length > 0) ? uploadUrls : null,
      additionalInfo: {
        advancedInfo,
        classifications
      }
    };
  }

  /**
   * Validate dữ liệu đầu vào cho sản phẩm số
   */
  private async validateDigitalProductData(dto: DigitalProductCreateDto): Promise<void> {
    // Kiểm tra advancedInfo có tồn tại không (bắt buộc cho sản phẩm số - chứa variants)
    if (!dto.advancedInfo) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Advanced info is required for digital products',
      );
    }

    // Kiểm tra price có tồn tại không (bắt buộc cho sản phẩm số)
    if (!dto.price) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Price is required for digital products',
      );
    }

    // Validate giá sản phẩm theo business rules
    this.validationHelper.validateProductPrice(dto.price, dto.typePrice, dto.productType);

    // Validate thông tin advanced info (variants)
    this.productValidationHelper.validateAdvancedInfo(dto.productType, dto.advancedInfo);

    this.logger.log(`Validated digital product data for: ${dto.name}`);
  }

  /**
   * Xử lý variant metadata cho sản phẩm số
   */
  private async processDigitalVariantMetadata(dto: DigitalProductCreateDto): Promise<any> {
    // TODO: Implement logic từ service gốc
    // Tạo metadata cho variants (biến thể sản phẩm số)
    
    this.logger.log(`Processing variant metadata for digital product: ${dto.name}`);
    
    // Placeholder return
    return {
      variants: dto.advancedInfo?.variants || [],
      // ... other variant metadata
    };
  }

  /**
   * Tạo advanced info cho sản phẩm số
   */
  private async createAdvancedInfo(
    productId: number,
    productType: string,
    advancedInfoDto: any,
    additionalData: any[],
  ): Promise<any> {
    this.logger.log(`Creating advanced info for digital product: ${productId}`);

    // TODO: Implement logic từ service gốc
    // Tạo bản ghi trong bảng product_advanced_info chứa thông tin variants
    
    // Placeholder return
    return {
      id: Date.now(), // Temporary ID
      productId,
      productType,
      data: advancedInfoDto,
      // ... other advanced info fields
    };
  }

  /**
   * Tạo upload URLs cho hình ảnh variants
   */
  private async createAdvancedImagesUploadUrls(
    advancedInfoDto: any,
    productType: string,
    timestamp: number,
  ): Promise<any[]> {
    const uploadUrls: any[] = [];

    // TODO: Implement logic từ service gốc
    // Xử lý hình ảnh cho variants
    
    this.logger.log(`Creating advanced images upload URLs for ${productType} product`);
    
    return uploadUrls;
  }

  /**
   * Cập nhật variant metadata với image keys
   */
  private async updateVariantMetadataWithImageKeys(
    product: UserProduct,
    advancedImagesUploadUrls: any[],
  ): Promise<void> {
    // TODO: Implement logic từ service gốc
    // Cập nhật variant metadata với image keys nếu có hình ảnh variants
    
    this.logger.log(`Updating variant metadata with image keys for product: ${product.id}`);
  }

  /**
   * Xử lý classifications cho sản phẩm
   */
  private async processClassifications(
    productId: number,
    classificationsDto: any[],
    userId: number,
  ): Promise<any[]> {
    if (!classificationsDto || classificationsDto.length === 0) {
      return [];
    }

    this.logger.log(`Processing ${classificationsDto.length} classifications for digital product: ${productId}`);

    // TODO: Implement logic từ service gốc
    // Tạo các bản ghi classification nếu có
    
    // Placeholder return
    return classificationsDto.map((classification, index) => ({
      id: index + 1,
      productId,
      name: classification.name || `Classification ${index + 1}`,
      // ... other classification fields
    }));
  }
}
