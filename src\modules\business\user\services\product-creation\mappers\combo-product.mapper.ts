import { Injectable } from '@nestjs/common';
import { UserProduct } from '@modules/business/entities/user-product.entity';
import { ComboProductResponseDto, ComboAdvancedInfoResponseDto } from '../../../dto/response/combo-product-response.dto';
import { ProductCreationResult } from '../handlers/base-product.handler';
import { BaseProductMapper } from './base-product.mapper';

/**
 * Mapper cho Combo Product
 * Chuyển đổi UserProduct entity sang ComboProductResponseDto
 */
@Injectable()
export class ComboProductMapper extends BaseProductMapper<ComboProductResponseDto> {
  /**
   * Map UserProduct entity sang ComboProductResponseDto
   */
  async mapToResponseDto(
    product: UserProduct,
    result: ProductCreationResult
  ): Promise<ComboProductResponseDto> {
    // Map common fields
    const commonFields = this.mapCommonFields(product, result);

    // Map combo-specific fields
    const comboResponse: ComboProductResponseDto = {
      ...commonFields,
      productType: product.productType as any, // COMBO
      advancedInfo: this.mapComboAdvancedInfo(result.advancedInfo),
      shipmentConfig: product.shipmentConfig,
      totalItems: this.calculateTotalItems(result.advancedInfo),
      isAvailable: this.checkAvailability(result.advancedInfo)
    } as ComboProductResponseDto;

    // Transform using class-transformer
    return this.transform(ComboProductResponseDto, comboResponse);
  }

  /**
   * Map advanced info cho combo product
   */
  private mapComboAdvancedInfo(advancedInfo: any): ComboAdvancedInfoResponseDto | undefined {
    if (!advancedInfo) {
      return undefined;
    }

    return {
      purchaseCount: advancedInfo.purchaseCount || 0,
      info: this.mapComboItems(advancedInfo.info),
      images: advancedInfo.images || [],
      calculatedPrice: this.calculateComboPrice(advancedInfo),
      savingInfo: this.calculateSavingInfo(advancedInfo)
    };
  }

  /**
   * Map combo items
   */
  private mapComboItems(comboItems: any[]): any[] {
    if (!comboItems || comboItems.length === 0) {
      return [];
    }

    return comboItems.map(item => ({
      productId: item.productId,
      total: item.total,
      productDetails: item.productDetails || undefined
    }));
  }

  /**
   * Calculate total items in combo
   */
  private calculateTotalItems(advancedInfo: any): number {
    if (!advancedInfo?.info) {
      return 0;
    }

    return advancedInfo.info.reduce((total: number, item: any) => total + item.total, 0);
  }

  /**
   * Check availability of combo
   */
  private checkAvailability(advancedInfo: any): boolean {
    if (!advancedInfo?.info) {
      return false;
    }

    // Logic kiểm tra tất cả sản phẩm trong combo có available không
    return advancedInfo.info.every((item: any) => {
      return item.productDetails?.status === 'ACTIVE' && 
             item.productDetails?.availableQuantity >= item.total;
    });
  }

  /**
   * Calculate combo price
   */
  private calculateComboPrice(advancedInfo: any): any {
    if (!advancedInfo?.calculatedPrice) {
      return undefined;
    }

    return {
      originalTotalPrice: advancedInfo.calculatedPrice.originalTotalPrice,
      discountAmount: advancedInfo.calculatedPrice.discountAmount,
      finalTotalPrice: advancedInfo.calculatedPrice.finalTotalPrice,
      currency: advancedInfo.calculatedPrice.currency || 'VND'
    };
  }

  /**
   * Calculate saving info
   */
  private calculateSavingInfo(advancedInfo: any): any {
    if (!advancedInfo?.savingInfo) {
      return undefined;
    }

    return {
      savingAmount: advancedInfo.savingInfo.savingAmount,
      savingPercentage: advancedInfo.savingInfo.savingPercentage,
      currency: advancedInfo.savingInfo.currency || 'VND'
    };
  }
}
