import { Injectable } from '@nestjs/common';
import { UserProduct } from '@modules/business/entities/user-product.entity';
import { EventProductResponseDto, EventAdvancedInfoResponseDto } from '../../../dto/response/event-product-response.dto';
import { ProductCreationResult } from '../handlers/base-product.handler';
import { BaseProductMapper } from './base-product.mapper';

/**
 * Mapper cho Event Product
 * Chuyển đổi UserProduct entity sang EventProductResponseDto
 */
@Injectable()
export class EventProductMapper extends BaseProductMapper<EventProductResponseDto> {
  /**
   * Map UserProduct entity sang EventProductResponseDto
   */
  async mapToResponseDto(
    product: UserProduct,
    result: ProductCreationResult
  ): Promise<EventProductResponseDto> {
    // Map common fields
    const commonFields = this.mapCommonFields(product, result);

    // Map event-specific fields
    const eventResponse: EventProductResponseDto = {
      ...commonFields,
      productType: product.productType as any, // EVENT
      advancedInfo: this.mapEventAdvancedInfo(result.advancedInfo, result.advancedImagesUploadUrls),
      shipmentConfig: {
        widthCm: 0,
        heightCm: 0,
        lengthCm: 0,
        weightGram: 0
      }
    } as EventProductResponseDto;

    // Transform using class-transformer
    return this.transform(EventProductResponseDto, eventResponse);
  }

  /**
   * Map advanced info cho event product
   */
  private mapEventAdvancedInfo(
    advancedInfo: any,
    advancedUploadUrls?: any[]
  ): EventAdvancedInfoResponseDto | undefined {
    if (!advancedInfo) {
      return undefined;
    }

    return {
      purchaseCount: advancedInfo.purchaseCount || 0,
      eventFormat: advancedInfo.eventFormat,
      eventLink: advancedInfo.eventLink,
      eventLocation: advancedInfo.eventLocation,
      startDate: advancedInfo.startDate,
      endDate: advancedInfo.endDate,
      timezone: advancedInfo.timezone,
      ticketTypes: this.mapTicketTypes(advancedInfo.ticketTypes, advancedUploadUrls),
      images: advancedInfo.images || []
    };
  }

  /**
   * Map ticket types với xử lý images từ upload URLs
   */
  private mapTicketTypes(ticketTypes: any[], advancedUploadUrls?: any[]): any[] {
    if (!ticketTypes || ticketTypes.length === 0) {
      return [];
    }

    return ticketTypes.map((ticketType, ticketIndex) => {
      // Tìm images cho ticket type này từ advanced upload URLs
      const ticketImages = this.getTicketTypeImages(ticketIndex, advancedUploadUrls);

      return {
        id: ticketType.id,
        name: ticketType.name,
        price: ticketType.price,
        startTime: ticketType.startTime,
        endTime: ticketType.endTime,
        timezone: ticketType.timezone,
        description: ticketType.description,
        quantity: ticketType.quantity,
        minQuantityPerPurchase: ticketType.minQuantityPerPurchase,
        maxQuantityPerPurchase: ticketType.maxQuantityPerPurchase,
        status: ticketType.status,
        images: ticketImages
      };
    });
  }

  /**
   * Lấy images cho ticket type từ advanced upload URLs
   */
  private getTicketTypeImages(ticketIndex: number, advancedUploadUrls?: any[]): any[] {
    if (!advancedUploadUrls || advancedUploadUrls.length === 0) {
      return [];
    }

    // Lọc images cho ticket type này (type: 'ticketType' và position: ticketIndex)
    const ticketTypeUrls = advancedUploadUrls.filter(
      url => url.type === 'ticketType' && url.position === ticketIndex
    );

    // Chuyển đổi thành format images với key, position, url
    return ticketTypeUrls.map(uploadUrl => ({
      key: uploadUrl.key,
      position: uploadUrl.index || 0,
      url: uploadUrl.url || ''
    }));
  }
}
