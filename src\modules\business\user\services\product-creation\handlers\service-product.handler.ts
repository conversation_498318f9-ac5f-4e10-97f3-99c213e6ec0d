import { Injectable } from '@nestjs/common';
import { UserProduct } from '@modules/business/entities/user-product.entity';
import { ProductTypeEnum, EntityStatusEnum } from '@modules/business/enums';
import { ServiceProductCreateDto } from '../../../dto/create/service-product-create.dto';
import { ServiceProductResponseDto } from '../../../dto/response/service-product-response.dto';
import { BaseProductHandler, ProductCreationContext, ProductCreationResult } from './base-product.handler';
import { ServiceProductMapper } from '../mappers/service-product.mapper';

/**
 * Handler cho Service Product
 * Xử lý logic riêng cho sản phẩm dịch vụ
 */
@Injectable()
export class ServiceProductHandler extends BaseProductHandler<
  ServiceProductCreateDto,
  ServiceProductResponseDto
> {
  readonly productType = ProductTypeEnum.SERVICE;

  constructor(
    private readonly serviceProductMapper: ServiceProductMapper,
    // Inject các services cần thiết
    // private readonly advancedInfoService: AdvancedInfoService,
    // private readonly classificationService: ClassificationService,
    // private readonly imageService: ImageService,
  ) {
    super();
  }

  /**
   * Validate dữ liệu đầu vào cho service product
   */
  async validate(dto: ServiceProductCreateDto, userId: number): Promise<void> {
    // Validate common fields
    this.validateCommonFields(dto);

    // Validate service-specific fields
    if (!dto.advancedInfo) {
      throw new Error('Advanced info is required for service products');
    }

    if (!dto.price) {
      throw new Error('Price is required for service products');
    }

    const serviceInfo = dto.advancedInfo;

    // Validate service packages
    if (!serviceInfo.servicePackages || serviceInfo.servicePackages.length === 0) {
      throw new Error('At least one service package is required');
    }

    for (const servicePackage of serviceInfo.servicePackages) {
      if (!servicePackage.name || servicePackage.name.trim().length === 0) {
        throw new Error('Service package name is required');
      }

      if (servicePackage.price !== undefined && servicePackage.price < 0) {
        throw new Error('Service package price cannot be negative');
      }

      if (!servicePackage.startTime || !servicePackage.endTime) {
        throw new Error('Service package start time and end time are required');
      }

      if (servicePackage.startTime >= servicePackage.endTime) {
        throw new Error('Service package start time must be before end time');
      }

      if (servicePackage.quantity !== undefined && servicePackage.quantity < 0) {
        throw new Error('Service package quantity cannot be negative');
      }

      if (servicePackage.minQuantityPerPurchase !== undefined && servicePackage.minQuantityPerPurchase < 0) {
        throw new Error('Service package minimum quantity per purchase cannot be negative');
      }

      if (servicePackage.maxQuantityPerPurchase !== undefined && servicePackage.maxQuantityPerPurchase < 0) {
        throw new Error('Service package maximum quantity per purchase cannot be negative');
      }

      if (servicePackage.minQuantityPerPurchase !== undefined && 
          servicePackage.maxQuantityPerPurchase !== undefined && 
          servicePackage.minQuantityPerPurchase > servicePackage.maxQuantityPerPurchase) {
        throw new Error('Service package minimum quantity cannot be greater than maximum quantity');
      }

      // Note: durationHours validation removed as it's not part of ServicePackageDto from service-package.dto.ts
    }

    // Validate service-specific fields
    if (dto.serviceTime !== undefined && dto.serviceTime < Date.now()) {
      throw new Error('Service time cannot be in the past');
    }

    if (dto.serviceDuration !== undefined) {
      const duration = parseInt(dto.serviceDuration);
      if (isNaN(duration) || duration <= 0) {
        throw new Error('Service duration must be a positive number');
      }
    }
  }

  /**
   * Tạo base product entity cho service product
   */
  async createBaseProduct(dto: ServiceProductCreateDto, userId: number): Promise<UserProduct> {
    // Xử lý service metadata
    const serviceMetadata = this.processServiceMetadata(dto);
    const { customFields, metadata } = await this.processCustomFields(dto, serviceMetadata);

    const product = new UserProduct();
    product.name = dto.name;
    product.productType = ProductTypeEnum.SERVICE;
    product.price = dto.price;
    product.typePrice = dto.typePrice;
    product.description = dto.description || '';
    product.images = []; // Sẽ được cập nhật sau khi xử lý images
    product.tags = dto.tags || [];
    product.metadata = metadata;
    product.createdBy = userId;
    product.createdAt = Date.now();
    product.updatedAt = Date.now();
    product.status = EntityStatusEnum.PENDING;

    // Set embedding fields to null
    product.nameEmbedding = null;
    product.descriptionEmbedding = null;
    product.tagsEmbedding = null;

    // Service products have zero shipment config
    product.shipmentConfig = {
      widthCm: 0,
      heightCm: 0,
      lengthCm: 0,
      weightGram: 0
    };

    return product;
  }

  /**
   * Xử lý advanced info cho service product
   */
  async processAdvancedInfo(
    product: UserProduct,
    dto: ServiceProductCreateDto,
    context: ProductCreationContext
  ): Promise<any> {
    // Logic tạo advanced info cho service product
    const advancedInfo = {
      productId: product.id,
      productType: ProductTypeEnum.SERVICE,
      purchaseCount: dto.advancedInfo.purchaseCount || 0,
      servicePackages: dto.advancedInfo.servicePackages,
      images: [], // Sẽ được cập nhật sau khi xử lý advanced images
      createdAt: context.timestamp,
      updatedAt: context.timestamp
    };

    // Logic lưu vào database
    // const savedAdvancedInfo = await this.advancedInfoService.create(advancedInfo);
    
    return advancedInfo;
  }

  /**
   * Xử lý inventory cho service product (không cần thiết cho service)
   */
  async processInventory(
    product: UserProduct,
    dto: ServiceProductCreateDto,
    context: ProductCreationContext
  ): Promise<any> {
    // Service products don't have traditional inventory
    // Inventory is managed through service packages
    return null;
  }

  /**
   * Xử lý classifications cho service product
   */
  async processClassifications(
    productId: number,
    dto: ServiceProductCreateDto,
    context: ProductCreationContext
  ): Promise<any[]> {
    if (!dto.classifications || dto.classifications.length === 0) {
      return [];
    }

    // Logic tạo classifications
    const classifications = [];
    for (const classificationDto of dto.classifications) {
      // const classification = await this.classificationService.create(productId, classificationDto, context.userId);
      // classifications.push(classification);
    }

    return classifications;
  }

  /**
   * Xử lý images upload URLs
   */
  async processImages(dto: ServiceProductCreateDto): Promise<{
    imageEntries: any[];
    imagesUploadUrls: any[];
  }> {
    const imageEntries: any[] = [];
    const imagesUploadUrls: any[] = [];
    const now = Date.now();

    if (dto.imagesMediaTypes && dto.imagesMediaTypes.length > 0) {
      for (let i = 0; i < dto.imagesMediaTypes.length; i++) {
        const mediaType = dto.imagesMediaTypes[i];
        const fileName = `service-product-image-${i}-${now}`;

        // Logic tạo presigned URL
        const key = `${fileName}.${this.getFileExtension(mediaType)}`;
        const url = `https://presigned-url-example.com/${key}`;

        imageEntries.push({
          key: key,
          position: i
        });

        imagesUploadUrls.push({
          url: url,
          key: key,
          index: i
        });
      }
    }

    return { imageEntries, imagesUploadUrls };
  }

  /**
   * Xử lý advanced images upload URLs
   */
  async processAdvancedImages(dto: ServiceProductCreateDto): Promise<{
    advancedImagesUploadUrls: any[];
  }> {
    const advancedImagesUploadUrls: any[] = [];
    const now = Date.now();

    // Xử lý service package images
    if (dto.advancedInfo.servicePackages && dto.advancedInfo.servicePackages.length > 0) {
      for (let packageIndex = 0; packageIndex < dto.advancedInfo.servicePackages.length; packageIndex++) {
        const servicePackage = dto.advancedInfo.servicePackages[packageIndex];
        
        if (servicePackage.imagesMediaTypes && servicePackage.imagesMediaTypes.length > 0) {
          for (let imageIndex = 0; imageIndex < servicePackage.imagesMediaTypes.length; imageIndex++) {
            const mediaType = servicePackage.imagesMediaTypes[imageIndex];
            const fileName = `service-package-${packageIndex}-image-${imageIndex}-${now}`;

            const key = `${fileName}.${this.getFileExtension(mediaType)}`;
            const url = `https://presigned-url-example.com/${key}`;

            advancedImagesUploadUrls.push({
              url: url,
              key: key,
              type: 'servicePackage',
              index: imageIndex,
              position: packageIndex
            });
          }
        }
      }
    }

    return { advancedImagesUploadUrls };
  }

  /**
   * Map entity sang response DTO
   */
  async mapToResponseDto(
    product: UserProduct,
    result: ProductCreationResult
  ): Promise<ServiceProductResponseDto> {
    return this.serviceProductMapper.mapToResponseDto(product, result);
  }

  /**
   * Private helper methods
   */
  private processServiceMetadata(dto: ServiceProductCreateDto): any {
    const serviceMetadata: any = {};

    if (dto.serviceTime !== undefined) {
      serviceMetadata.serviceTime = dto.serviceTime;
    }

    if (dto.serviceDuration !== undefined) {
      serviceMetadata.serviceDuration = dto.serviceDuration;
    }

    if (dto.serviceProvider !== undefined) {
      serviceMetadata.serviceProvider = dto.serviceProvider;
    }

    if (dto.serviceType !== undefined) {
      serviceMetadata.serviceType = dto.serviceType;
    }

    if (dto.serviceLocation !== undefined) {
      serviceMetadata.serviceLocation = dto.serviceLocation;
    }

    return serviceMetadata;
  }

  private getFileExtension(mediaType: string): string {
    const extensions: { [key: string]: string } = {
      'image/jpeg': 'jpg',
      'image/jpg': 'jpg',
      'image/png': 'png',
      'image/gif': 'gif',
      'image/webp': 'webp'
    };
    return extensions[mediaType] || 'jpg';
  }
}
