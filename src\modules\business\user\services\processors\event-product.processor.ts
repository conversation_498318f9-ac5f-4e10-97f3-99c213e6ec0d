import { Injectable, Logger } from '@nestjs/common';
import {
  ProductAdvancedInfoRepository,
} from '@modules/business/repositories';
import { EventProductCreateDto } from '../../dto';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { UserProduct } from '@modules/business/entities';
import { PriceTypeEnum } from '@modules/business/enums';
import { ClassificationService } from '../classification.service';
import { CreateProductProcessor } from './create-product.processor';
import { CreateProductResult } from './create-product-orchestrator';
import { S3Service } from '@shared/services/s3.service';

/**
 * Processor chuyên xử lý logic tạo sản phẩm sự kiện
 * Bóc tách từ UserProductService để tối ưu hóa và dễ maintain
 */
@Injectable()
export class EventProductProcessor {
  private readonly logger = new Logger(EventProductProcessor.name);

  constructor(
    private readonly createProcessor: CreateProductProcessor,
    private readonly productAdvancedInfoRepository: ProductAdvancedInfoRepository,
    private readonly classificationService: ClassificationService,
    private readonly s3Service: S3Service,
  ) {}

  /**
   * Tạo sản phẩm sự kiện hoàn chỉnh
   */
  async createEventProduct(
    dto: EventProductCreateDto,
    userId: number,
  ): Promise<CreateProductResult> {
    this.logger.log(`Creating EVENT product: ${dto.name}`);

    // BƯỚC 1: Validate dữ liệu đầu vào cho sản phẩm sự kiện
    await this.validateEventProductData(dto);

    // BƯỚC 2: Xử lý custom fields
    const { customFields, metadata } = await this.createProcessor.processCustomFields(dto);

    // BƯỚC 3: Tạo sản phẩm cơ bản với shipment config = 0 và xử lý giá đặc biệt
    const product = await this.createProcessor.createBaseProduct(dto, userId, metadata);
    product.shipmentConfig = { widthCm: 0, heightCm: 0, lengthCm: 0, weightGram: 0 };

    // Sản phẩm sự kiện có thể có giá null (giá sẽ lấy từ ticket types)
    if (!dto.price) {
      product.price = null;
      product.typePrice = dto.typePrice || PriceTypeEnum.HAS_PRICE;
      this.logger.log('EVENT product - setting price to null and keeping typePrice as HAS_PRICE');
    }

    // BƯỚC 4: Lưu sản phẩm để có ID
    const savedProduct = await this.createProcessor.saveProduct(product);

    // BƯỚC 5: Xử lý hình ảnh sản phẩm chính
    const { imageEntries, imagesUploadUrls } = await this.createProcessor.processProductImages(dto, Date.now());

    // BƯỚC 6: Cập nhật sản phẩm với thông tin hình ảnh
    savedProduct.images = imageEntries;
    await this.createProcessor.saveProduct(savedProduct);

    // BƯỚC 7: Tạo advanced info (ticket types)
    const advancedInfo = await this.createAdvancedInfo(savedProduct.id, dto.productType, dto.advancedInfo, []);

    // BƯỚC 8: Xử lý hình ảnh cho ticket types
    const advancedImagesUploadUrls = await this.createAdvancedImagesUploadUrls(dto.advancedInfo, dto.productType, Date.now());

    // BƯỚC 9: Cập nhật detail_id để liên kết với advanced info
    if (advancedInfo?.id) {
      savedProduct.detail_id = advancedInfo.id;
      await this.createProcessor.saveProduct(savedProduct);

      // Cập nhật advanced info với image keys nếu có hình ảnh ticket types
      if (advancedImagesUploadUrls.length > 0) {
        await this.updateAdvancedInfoWithImageKeys(advancedInfo.id, dto.productType, advancedImagesUploadUrls);
      }
    }

    // BƯỚC 10: Xử lý classifications
    const classifications = await this.processClassifications(savedProduct.id, dto.classifications || [], userId);

    // BƯỚC 11: Lấy sản phẩm cuối cùng SAU KHI đã cập nhật advanced info với image keys
    const finalProduct = await this.createProcessor.getProductById(savedProduct.id);

    // BƯỚC 12: Tạo object chứa upload URLs cho cả sản phẩm chính và ticket types
    const uploadUrls: any = {
      productId: finalProduct.id.toString(),
      imagesUploadUrls: imagesUploadUrls || []
    };

    // Thêm upload URLs cho hình ảnh ticket types nếu có
    if (advancedImagesUploadUrls && advancedImagesUploadUrls.length > 0) {
      uploadUrls.advancedImagesUploadUrls = advancedImagesUploadUrls;
    }

    return {
      product: finalProduct,
      uploadUrls: (imagesUploadUrls.length > 0 || advancedImagesUploadUrls.length > 0) ? uploadUrls : null,
      additionalInfo: {
        advancedInfo,
        classifications
      }
    };
  }

  /**
   * Validate dữ liệu đầu vào cho sản phẩm sự kiện
   */
  private async validateEventProductData(dto: EventProductCreateDto): Promise<void> {
    // Kiểm tra advancedInfo có tồn tại không (bắt buộc cho sản phẩm sự kiện - chứa ticket types)
    if (!dto.advancedInfo) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        'Advanced info is required for event products',
      );
    }

    // Bỏ qua validation advanced info cho cấu trúc DTO mới
    // this.productValidationHelper.validateAdvancedInfo(dto.productType, dto.advancedInfo);

    this.logger.log(`Validated event product data for: ${dto.name}`);
  }

  /**
   * Tạo advanced info cho sản phẩm sự kiện
   */
  private async createAdvancedInfo(
    productId: number,
    productType: string,
    advancedInfoDto: any,
    additionalData: any[],
  ): Promise<any> {
    this.logger.log(`Creating advanced info for event product: ${productId}`);

    // TODO: Implement logic từ service gốc
    // Tạo bản ghi trong bảng product_advanced_info chứa thông tin ticket types
    
    // Placeholder return
    return {
      id: Date.now(), // Temporary ID
      productId,
      productType,
      data: advancedInfoDto,
      // ... other advanced info fields
    };
  }

  /**
   * Tạo upload URLs cho hình ảnh ticket types
   */
  private async createAdvancedImagesUploadUrls(
    advancedInfoDto: any,
    productType: string,
    timestamp: number,
  ): Promise<any[]> {
    const uploadUrls: any[] = [];

    // TODO: Implement logic từ service gốc
    // Xử lý hình ảnh cho ticket types
    
    this.logger.log(`Creating advanced images upload URLs for ${productType} product`);
    
    return uploadUrls;
  }

  /**
   * Cập nhật advanced info với image keys
   */
  private async updateAdvancedInfoWithImageKeys(
    advancedInfoId: number,
    productType: string,
    advancedImagesUploadUrls: any[],
  ): Promise<void> {
    // TODO: Implement logic từ service gốc
    // Cập nhật advanced info với image keys nếu có hình ảnh ticket types
    
    this.logger.log(`Updating advanced info with image keys for ${productType} product: ${advancedInfoId}`);
  }

  /**
   * Xử lý classifications cho sản phẩm
   */
  private async processClassifications(
    productId: number,
    classificationsDto: any[],
    userId: number,
  ): Promise<any[]> {
    if (!classificationsDto || classificationsDto.length === 0) {
      return [];
    }

    this.logger.log(`Processing ${classificationsDto.length} classifications for event product: ${productId}`);

    // TODO: Implement logic từ service gốc
    // Tạo các bản ghi classification nếu có
    
    // Placeholder return
    return classificationsDto.map((classification, index) => ({
      id: index + 1,
      productId,
      name: classification.name || `Classification ${index + 1}`,
      // ... other classification fields
    }));
  }
}
