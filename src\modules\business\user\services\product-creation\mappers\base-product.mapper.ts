import { Injectable } from '@nestjs/common';
import { plainToClass } from 'class-transformer';
import { UserProduct } from '@modules/business/entities/user-product.entity';
import { BaseProductResponseDto } from '../../../dto/base/base-product-response.dto';
import { ProductCreationResult } from '../handlers/base-product.handler';

/**
 * Abstract Base Mapper cho tất cả các loại sản phẩm
 * Định nghĩa interface chung cho việc mapping entity sang response DTO
 */
@Injectable()
export abstract class BaseProductMapper<TResponseDto extends BaseProductResponseDto> {
  /**
   * Map UserProduct entity sang response DTO
   */
  abstract mapToResponseDto(
    product: UserProduct,
    result: ProductCreationResult
  ): Promise<TResponseDto>;

  /**
   * Map common fields từ entity sang response DTO
   */
  protected mapCommonFields(product: UserProduct, result: ProductCreationResult): Partial<BaseProductResponseDto> {
    return {
      id: product.id,
      name: product.name,
      productType: product.productType,
      typePrice: product.typePrice,
      price: product.price,
      description: product.description,
      images: product.images,
      tags: product.tags,
      metadata: product.metadata,
      shipmentConfig: product.shipmentConfig,
      status: product.status,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
      createdBy: product.createdBy,
      classifications: result.classifications,
      uploadUrls: this.buildUploadUrls(product, result)
    };
  }

  /**
   * Build upload URLs object
   */
  protected buildUploadUrls(product: UserProduct, result: ProductCreationResult): any {
    const uploadUrls: any = {
      productId: product.id.toString(),
      imagesUploadUrls: result.imagesUploadUrls || []
    };

    // Thêm advanced images upload URLs nếu có
    if (result.advancedImagesUploadUrls && result.advancedImagesUploadUrls.length > 0) {
      uploadUrls.advancedImagesUploadUrls = result.advancedImagesUploadUrls;
    }

    return uploadUrls;
  }

  /**
   * Transform object using class-transformer
   */
  protected transform<T>(cls: new () => T, obj: any): T {
    return plainToClass(cls, obj, {
      excludeExtraneousValues: true,
      enableImplicitConversion: true
    });
  }

  /**
   * Map advanced info nếu có
   */
  protected mapAdvancedInfo(advancedInfo: any): any {
    if (!advancedInfo) {
      return undefined;
    }

    // Base mapping cho advanced info
    return {
      ...advancedInfo,
      // Có thể thêm logic mapping chung ở đây
    };
  }

  /**
   * Map inventory info nếu có
   */
  protected mapInventory(inventory: any): any {
    if (!inventory) {
      return undefined;
    }

    return {
      id: inventory.id,
      warehouseId: inventory.warehouseId,
      availableQuantity: inventory.availableQuantity,
      minQuantity: inventory.minQuantity,
      maxQuantity: inventory.maxQuantity,
      sku: inventory.sku,
      barcode: inventory.barcode,
      unit: inventory.unit,
      createdAt: inventory.createdAt,
      updatedAt: inventory.updatedAt
    };
  }

  /**
   * Map classifications nếu có
   */
  protected mapClassifications(classifications: any[]): any[] {
    if (!classifications || classifications.length === 0) {
      return [];
    }

    return classifications.map(classification => ({
      id: classification.id,
      type: classification.type,
      price: classification.price,
      images: classification.images,
      customFields: classification.customFields,
      createdAt: classification.createdAt,
      updatedAt: classification.updatedAt
    }));
  }
}
