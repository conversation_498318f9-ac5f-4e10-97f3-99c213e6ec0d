import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { plainToInstance } from 'class-transformer';
import {
  UserProductRepository,
  CustomFieldRepository,
  InventoryRepository,
  PhysicalWarehouseRepository,
  ProductAdvancedInfoRepository,
} from '@modules/business/repositories';
import {
  BusinessUpdateProductDto,
  BusinessProductResponseDto as ProductResponseDto,
  ClassificationResponseDto,
  CreateClassificationDto,
  ProductInventoryDto,
} from '../../dto';
import { ProductTypeEnum, PriceTypeEnum } from '@modules/business/enums';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { UserProduct } from '@modules/business/entities';
import { UserProductHelper } from '../../helpers/user-product.helper';
import { MetadataHelper } from '../../helpers/metadata.helper';
import { ValidationHelper } from '../../helpers/validation.helper';
import { ProductValidationHelper } from '../../helpers/product-validation.helper';
import { ClassificationService } from '../classification.service';
import { DataSource } from 'typeorm';
import { InventoryResponseDto } from '../../dto/inventory';

/**
 * Processor chuyên xử lý logic cập nhật sản phẩm
 * Bóc tách từ UserProductService để tối ưu hóa và dễ maintain
 */
@Injectable()
export class UpdateProductProcessor {
  private readonly logger = new Logger(UpdateProductProcessor.name);

  constructor(
    private readonly userProductRepository: UserProductRepository,
    private readonly customFieldRepository: CustomFieldRepository,
    private readonly inventoryRepository: InventoryRepository,
    private readonly physicalWarehouseRepository: PhysicalWarehouseRepository,
    private readonly productAdvancedInfoRepository: ProductAdvancedInfoRepository,
    private readonly userProductHelper: UserProductHelper,
    private readonly metadataHelper: MetadataHelper,
    private readonly validationHelper: ValidationHelper,
    private readonly productValidationHelper: ProductValidationHelper,
    private readonly classificationService: ClassificationService,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Tìm và validate sản phẩm tồn tại
   */
  async findAndValidateProduct(
    id: number,
    userId: number,
  ): Promise<UserProduct> {
    const product = await this.userProductRepository.findByIdAndUserId(
      id,
      userId,
    );

    if (!product) {
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
        `Không tìm thấy sản phẩm với ID ${id}`,
      );
    }

    return product;
  }

  /**
   * Cập nhật thông tin cơ bản của sản phẩm
   */
  updateBasicFields(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): void {
    // Cập nhật name
    if (updateDto.name !== undefined) {
      product.name = updateDto.name;
    }

    // Cập nhật productType
    if (updateDto.productType !== undefined) {
      product.productType = updateDto.productType;
    }

    // Cập nhật description
    if (updateDto.description !== undefined) {
      product.description = updateDto.description;
    }

    // Cập nhật tags
    if (updateDto.tags !== undefined) {
      product.tags = updateDto.tags;
    }

    // Cập nhật shipmentConfig
    if (updateDto.shipmentConfig !== undefined) {
      product.shipmentConfig = updateDto.shipmentConfig;
    }
  }

  /**
   * Xử lý cập nhật giá sản phẩm theo loại
   */
  updateProductPricing(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): void {
    const finalProductType =
      updateDto.productType !== undefined
        ? updateDto.productType
        : product.productType;

    if (finalProductType === ProductTypeEnum.EVENT) {
      // Xử lý đặc biệt cho EVENT products
      product.price = null;
      product.typePrice = product.typePrice || PriceTypeEnum.HAS_PRICE;
      this.logger.log(
        'EVENT product detected in update - setting price to null and keeping typePrice as HAS_PRICE',
      );
    } else {
      // Xử lý cho các loại sản phẩm khác
      this.updateNormalProductPricing(product, updateDto);
    }
  }

  /**
   * Cập nhật giá cho sản phẩm thông thường (không phải EVENT)
   */
  private updateNormalProductPricing(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): void {
    if (updateDto.price !== undefined && updateDto.typePrice !== undefined) {
      // Validate giá khi có cả price và typePrice
      this.validationHelper.validateProductPrice(
        updateDto.price,
        updateDto.typePrice,
      );
      product.price = updateDto.price;
      product.typePrice = updateDto.typePrice;
    } else if (updateDto.price !== undefined) {
      // Chỉ cập nhật price
      product.price = updateDto.price;
    } else if (updateDto.typePrice !== undefined) {
      // Chỉ cập nhật typePrice
      product.typePrice = updateDto.typePrice;
    }
  }

  /**
   * Xử lý cập nhật custom fields
   */
  async updateCustomFields(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): Promise<void> {
    if (updateDto.customFields === undefined) {
      return;
    }

    let customFields: any[] = [];

    if (updateDto.customFields && updateDto.customFields.length > 0) {
      this.logger.log(
        `Xử lý ${updateDto.customFields.length} custom fields cho sản phẩm`,
      );

      // Lấy danh sách ID custom fields
      const customFieldIds = this.metadataHelper.extractCustomFieldIds(
        updateDto.customFields,
      );

      // Lấy thông tin chi tiết custom fields từ database
      customFields = await this.customFieldRepository.findByIds(customFieldIds);

      // Validate custom fields
      this.metadataHelper.validateCustomFieldInputs(
        updateDto.customFields,
        customFields,
      );
    }

    // Xử lý metadata
    const additionalMetadata = this.preserveExistingMetadata(product);
    await this.updateVariantMetadata(product, updateDto, additionalMetadata);
    this.updateServiceMetadata(product, updateDto, additionalMetadata);

    // Cập nhật metadata cho sản phẩm
    const metadata = this.metadataHelper.buildMetadata(
      updateDto.customFields,
      customFields,
      additionalMetadata,
    );
    product.metadata = metadata;
  }

  /**
   * Bảo tồn metadata hiện có
   */
  private preserveExistingMetadata(product: UserProduct): any {
    const additionalMetadata: any = {};

    // Giữ lại variant metadata hiện tại nếu có
    if (product.metadata?.variants) {
      additionalMetadata.variants = product.metadata.variants;
    }

    // Giữ lại service metadata hiện tại nếu có
    const serviceFields = [
      'serviceTime',
      'serviceDuration',
      'serviceProvider',
      'serviceType',
      'serviceLocation',
    ];
    serviceFields.forEach((field) => {
      if (product.metadata?.[field] !== undefined) {
        additionalMetadata[field] = product.metadata[field];
      }
    });

    return additionalMetadata;
  }

  /**
   * Cập nhật variant metadata cho sản phẩm số
   */
  private async updateVariantMetadata(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
    additionalMetadata: any,
  ): Promise<void> {
    const digitalAdvancedInfo = updateDto.advancedInfo as any;

    if (
      !digitalAdvancedInfo?.variantMetadata?.variants ||
      product.productType !== ProductTypeEnum.DIGITAL
    ) {
      return;
    }

    this.logger.log(
      `Cập nhật variant metadata cho sản phẩm số với ${digitalAdvancedInfo.variantMetadata.variants.length} phiên bản`,
    );

    const processedVariants = await Promise.all(
      digitalAdvancedInfo.variantMetadata.variants.map(
        async (variant: any, index: number) => {
          return await this.processVariant(
            variant,
            index,
            product.metadata?.variants?.[index],
          );
        },
      ),
    );

    additionalMetadata.variants = processedVariants;
  }

  /**
   * Xử lý từng variant
   */
  private async processVariant(
    variant: any,
    index: number,
    existingVariant: any,
  ): Promise<any> {
    const processedVariant = { ...variant };

    // Xử lý image operations
    await this.processVariantImageOperations(
      processedVariant,
      index,
      existingVariant,
    );

    // Xử lý custom fields cho variant
    await this.processVariantCustomFields(processedVariant, existingVariant);

    // Cleanup
    delete processedVariant.imagesMediaTypes;
    delete processedVariant.imageOperations;

    return processedVariant;
  }

  /**
   * Xử lý image operations cho variant
   */
  private async processVariantImageOperations(
    processedVariant: any,
    index: number,
    existingVariant: any,
  ): Promise<void> {
    const imageOperations =
      processedVariant.imageOperations || processedVariant.images;

    if (!imageOperations || !Array.isArray(imageOperations)) {
      if (existingVariant?.images && !processedVariant.imagesMediaTypes) {
        processedVariant.images = existingVariant.images;
      }
      return;
    }

    let currentImages = existingVariant?.images
      ? [...existingVariant.images]
      : [];
    const now = Date.now();

    // Xử lý DELETE operations
    const deleteOperations = imageOperations.filter(
      (op: any) => op.operation === 'DELETE',
    );
    for (const deleteOp of deleteOperations) {
      if (deleteOp.key) {
        currentImages = currentImages.filter(
          (imageKey: string) => imageKey !== deleteOp.key,
        );
        this.logger.debug(`Xóa variant image với key: ${deleteOp.key}`);
      }
    }

    // Xử lý ADD operations
    const addOperations = imageOperations.filter(
      (op: any) => op.operation === 'ADD',
    );
    for (let i = 0; i < addOperations.length; i++) {
      const placeholderKey = `variant-${index}-image-${i}-${now}`;
      currentImages.push(placeholderKey);
    }

    processedVariant.images = currentImages;
  }

  /**
   * Xử lý custom fields cho variant
   */
  private async processVariantCustomFields(
    processedVariant: any,
    existingVariant: any,
  ): Promise<void> {
    if (
      !processedVariant.customFields ||
      processedVariant.customFields.length === 0
    ) {
      if (existingVariant?.customFields) {
        processedVariant.customFields = existingVariant.customFields;
      }
      return;
    }

    this.logger.log(
      `Cập nhật ${processedVariant.customFields.length} custom fields cho variant ${processedVariant.name}`,
    );

    // Lấy danh sách ID custom fields
    const variantCustomFieldIds = this.metadataHelper.extractCustomFieldIds(
      processedVariant.customFields,
    );

    // Lấy thông tin chi tiết custom fields từ database
    const variantCustomFields = await this.customFieldRepository.findByIds(
      variantCustomFieldIds,
    );

    // Validate custom fields
    this.metadataHelper.validateCustomFieldInputs(
      processedVariant.customFields,
      variantCustomFields,
    );

    // Build metadata cho variant custom fields
    const variantMetadata = this.metadataHelper.buildMetadataCustomFields(
      processedVariant.customFields,
      variantCustomFields,
    );

    processedVariant.customFields = variantMetadata;
  }

  /**
   * Cập nhật service metadata
   */
  private updateServiceMetadata(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
    additionalMetadata: any,
  ): void {
    if (product.productType !== ProductTypeEnum.SERVICE) {
      return;
    }

    const serviceFields = [
      'serviceTime',
      'serviceDuration',
      'serviceProvider',
      'serviceType',
      'serviceLocation',
    ];

    let hasUpdates = false;
    serviceFields.forEach((field) => {
      if (updateDto[field] !== undefined) {
        additionalMetadata[field] = updateDto[field];
        hasUpdates = true;
      }
    });

    if (hasUpdates) {
      this.logger.log(`Cập nhật service metadata cho sản phẩm dịch vụ`);
    }
  }

  /**
   * Finalize product update
   */
  finalizeProductUpdate(product: UserProduct): void {
    // Đảm bảo các trường embedding vẫn là null
    product.nameEmbedding = null;
    product.descriptionEmbedding = null;
    product.tagsEmbedding = null;

    // Cập nhật thời gian
    product.updatedAt = Date.now();
  }

  /**
   * Lưu sản phẩm đã cập nhật
   */
  async saveUpdatedProduct(product: UserProduct): Promise<UserProduct> {
    return await this.userProductRepository.save(product);
  }

  /**
   * Xử lý cập nhật advanced info
   */
  async processAdvancedInfoUpdate(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
    now: number,
  ): Promise<{
    advancedImagesUploadUrls: Array<{
      url: string;
      key: string;
      type: string;
      index: number;
      position: number;
    }>;
    advancedInfo?: any;
  }> {
    let advancedImagesUploadUrls: Array<{
      url: string;
      key: string;
      type: string;
      index: number;
      position: number;
    }> = [];
    let advancedInfo: any;

    if (!updateDto.advancedInfo) {
      return { advancedImagesUploadUrls };
    }

    // Validate productType có hỗ trợ advancedInfo không
    if (product.productType === ProductTypeEnum.PHYSICAL) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Sản phẩm vật lý không được có thông tin nâng cao',
      );
    }

    try {
      // Validate advancedInfo theo productType
      this.productValidationHelper.validateAdvancedInfo(
        product.productType,
        updateDto.advancedInfo,
      );

      // Tạo presigned URLs cho advanced images
      advancedImagesUploadUrls = await this.createAdvancedImagesUploadUrls(
        updateDto.advancedInfo,
        product.productType,
        now,
      );

      // Cập nhật advanced info
      advancedInfo = await this.updateAdvancedInfo(
        product.id,
        product.productType,
        updateDto.advancedInfo,
      );

      // Cập nhật detail_id nếu chưa có
      if (advancedInfo?.id && !product.detail_id) {
        product.detail_id = advancedInfo.id;
        await this.userProductRepository.save(product);
        this.logger.log(
          `Updated product ${product.id} with detail_id: ${advancedInfo.id} during update`,
        );
      }

      // Cập nhật advanced info với S3 keys nếu có advanced images
      if (advancedImagesUploadUrls.length > 0) {
        await this.updateAdvancedInfoWithImageKeys(
          product,
          advancedInfo,
          advancedImagesUploadUrls,
        );
      }
    } catch (advancedInfoError) {
      this.logger.warn(
        `Không thể cập nhật thông tin nâng cao cho sản phẩm ${product.id}: ${advancedInfoError.message}`,
      );
    }

    return { advancedImagesUploadUrls, advancedInfo };
  }

  /**
   * Xử lý cập nhật classifications
   */
  async processClassificationsUpdate(
    productId: number,
    updateDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<{
    classifications: ClassificationResponseDto[];
    classificationUploadUrls: any[];
  }> {
    const classifications: ClassificationResponseDto[] = [];
    const classificationUploadUrls: any[] = [];

    if (!updateDto.classifications || updateDto.classifications.length === 0) {
      return { classifications, classificationUploadUrls };
    }

    try {
      for (const classificationDto of updateDto.classifications) {
        const result = await this.processSingleClassification(
          productId,
          classificationDto,
          userId,
        );

        classifications.push(result.classification);
        if (result.uploadUrls) {
          classificationUploadUrls.push(...result.uploadUrls);
        }
      }
    } catch (classificationError) {
      this.logger.warn(
        `Không thể cập nhật phân loại cho sản phẩm ${productId}: ${classificationError.message}`,
      );
    }

    return { classifications, classificationUploadUrls };
  }

  /**
   * Xử lý từng classification
   */
  private async processSingleClassification(
    productId: number,
    classificationDto: any,
    userId: number,
  ): Promise<{
    classification: ClassificationResponseDto;
    uploadUrls?: any[];
  }> {
    if (classificationDto.id) {
      // Cập nhật classification hiện có
      const classification = await this.classificationService.update(
        classificationDto.id,
        classificationDto,
        userId,
      );

      this.logger.log(
        `Classification update response: ${JSON.stringify(classification)}`,
      );

      let uploadUrls: any[] = [];
      if (classification.uploadUrls?.imagesUploadUrls) {
        this.logger.log(
          `Found ${classification.uploadUrls.imagesUploadUrls.length} upload URLs for classification ${classificationDto.id}`,
        );
        uploadUrls = classification.uploadUrls.imagesUploadUrls;
        delete classification.uploadUrls;
      }

      return { classification, uploadUrls };
    } else {
      // Tạo classification mới
      return await this.createNewClassification(
        productId,
        classificationDto,
        userId,
      );
    }
  }

  /**
   * Tạo classification mới
   */
  private async createNewClassification(
    productId: number,
    classificationDto: any,
    userId: number,
  ): Promise<{
    classification: ClassificationResponseDto;
    uploadUrls?: any[];
  }> {
    if (!classificationDto.type) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVALID_INPUT,
        'Loại phân loại (type) là bắt buộc khi tạo phân loại mới',
      );
    }

    const createClassificationDto: CreateClassificationDto = {
      type: classificationDto.type,
      price: classificationDto.price,
      customFields: classificationDto.customFields,
      imagesMediaTypes: classificationDto.imagesMediaTypes,
    };

    const classification = await this.classificationService.create(
      productId,
      createClassificationDto,
      userId,
    );

    let uploadUrls: any[] = [];
    if (classification.uploadUrls?.imagesUploadUrls) {
      uploadUrls = classification.uploadUrls.imagesUploadUrls;
      delete classification.uploadUrls;
    }

    return { classification, uploadUrls };
  }

  /**
   * Xử lý xóa classifications
   */
  async processClassificationsDeletion(
    productId: number,
    classificationsToDelete: number[],
    userId: number,
  ): Promise<void> {
    if (!classificationsToDelete || classificationsToDelete.length === 0) {
      return;
    }

    try {
      for (const classificationId of classificationsToDelete) {
        await this.classificationService.delete(classificationId, userId);
      }
      this.logger.log(
        `Đã xóa ${classificationsToDelete.length} phân loại cho sản phẩm ${productId}`,
      );
    } catch (classificationDeleteError) {
      this.logger.warn(
        `Không thể xóa phân loại cho sản phẩm ${productId}: ${classificationDeleteError.message}`,
      );
    }
  }

  /**
   * Validate và xử lý inventory cho sản phẩm PHYSICAL
   */
  async validatePhysicalProductInventory(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
  ): Promise<void> {
    if (product.productType !== ProductTypeEnum.PHYSICAL) {
      return;
    }

    // Kiểm tra xem có inventory hiện tại không
    const existingInventoriesResult = await this.inventoryRepository.findAll({
      productId: product.id,
      limit: 1,
    });
    const hasExistingInventory =
      existingInventoriesResult.items &&
      existingInventoriesResult.items.length > 0;

    // Nếu không có inventory hiện tại và cũng không có inventory trong request, báo lỗi
    if (!hasExistingInventory && !updateDto.inventory) {
      throw new AppException(
        BUSINESS_ERROR_CODES.INVENTORY_VALIDATION_FAILED,
        `Thông tin tồn kho là bắt buộc đối với sản phẩm loại ${product.productType}`,
      );
    }
  }

  /**
   * Xử lý cập nhật inventory
   */
  async processInventoryUpdate(
    product: UserProduct,
    updateDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<InventoryResponseDto | undefined> {
    if (!updateDto.inventory) {
      return undefined;
    }

    try {
      this.logger.log(
        `Bắt đầu cập nhật inventory cho sản phẩm ${product.id} với dữ liệu: ${JSON.stringify(updateDto.inventory)}`,
      );

      if (updateDto.inventory.inventoryId) {
        // Sử dụng inventory đã có
        const inventory = await this.useExistingInventory(
          product.id,
          updateDto.inventory.inventoryId,
          userId,
        );
        this.logger.log(
          `Sử dụng inventory có sẵn thành công cho sản phẩm ${product.id}: ${JSON.stringify(inventory)}`,
        );
        return inventory;
      } else {
        // Tạo/cập nhật inventory
        const inventory = await this.createOrUpdateProductInventory(
          product.id,
          updateDto.inventory,
          userId,
        );
        this.logger.log(
          `Cập nhật inventory thành công cho sản phẩm ${product.id}: ${JSON.stringify(inventory)}`,
        );
        return inventory;
      }
    } catch (inventoryError) {
      this.logger.error(
        `Lỗi khi cập nhật tồn kho cho sản phẩm ${product.id}: ${inventoryError.message}`,
        inventoryError.stack,
      );

      // Nếu là lỗi validation quan trọng, throw lỗi
      if (this.isImportantInventoryError(inventoryError)) {
        throw inventoryError;
      }

      // Các lỗi khác chỉ log warning
      this.logger.warn(
        `Không thể cập nhật tồn kho cho sản phẩm ${product.id}: ${inventoryError.message}`,
      );
      return undefined;
    }
  }

  /**
   * Kiểm tra có phải lỗi inventory quan trọng không
   */
  private isImportantInventoryError(error: any): boolean {
    if (!(error instanceof AppException)) {
      return false;
    }

    const importantErrors = [
      'SKU',
      'đã tồn tại',
      'Inventory',
      'không tồn tại',
      'đã được sử dụng cho sản phẩm khác',
    ];

    return importantErrors.some((keyword) => error.message.includes(keyword));
  }

  /**
   * Xây dựng response cuối cùng
   */
  async buildUpdateResponse(
    product: UserProduct,
    imagesUploadUrls: any[],
    advancedImagesUploadUrls: any[],
    classificationUploadUrls: any[],
    classifications: ClassificationResponseDto[],
    inventory?: InventoryResponseDto,
  ): Promise<ProductResponseDto> {
    // Chuyển đổi thành DTO response
    const productResponse =
      await this.userProductHelper.mapToProductResponseDto(product);
    const response: any = { ...productResponse };

    // Thêm advanced info nếu có
    await this.addAdvancedInfoToResponse(product, response);

    // Thêm upload URLs nếu có
    this.addUploadUrlsToResponse(
      response,
      product.id,
      imagesUploadUrls,
      advancedImagesUploadUrls,
      classificationUploadUrls,
    );

    // Thêm classifications nếu có
    if (classifications.length > 0) {
      response.classifications = classifications;
    }

    // Thêm inventory
    await this.addInventoryToResponse(product, response, inventory);

    return response;
  }

  /**
   * Thêm advanced info vào response
   */
  private async addAdvancedInfoToResponse(
    product: UserProduct,
    response: any,
  ): Promise<void> {
    if (!product.detail_id) {
      return;
    }

    try {
      const advancedInfo = await this.productAdvancedInfoRepository.findOne({
        where: { id: product.detail_id },
      });

      if (!advancedInfo) {
        return;
      }

      this.logger.log(
        `Retrieved advanced info for response: ticketTypes=${advancedInfo.ticketTypes ? JSON.stringify(advancedInfo.ticketTypes) : 'null'}`,
      );

      // Xử lý advanced info theo loại sản phẩm
      switch (product.productType) {
        case ProductTypeEnum.EVENT:
          response.advancedInfo = {
            purchaseCount: advancedInfo.purchaseCount,
            eventFormat: advancedInfo.eventFormat,
            eventLink: advancedInfo.eventLink,
            eventLocation: advancedInfo.eventLocation,
            startDate: advancedInfo.startDate,
            endDate: advancedInfo.endDate,
            timezone: advancedInfo.timezone,
            ticketTypes: await this.processTicketTypesWithImages(
              advancedInfo.ticketTypes || [],
            ),
          };
          break;

        case ProductTypeEnum.SERVICE:
          response.advancedInfo = {
            purchaseCount: advancedInfo.purchaseCount,
            servicePackages: await this.processServicePackagesWithImages(
              advancedInfo.servicePackages || [],
            ),
          };
          break;

        case ProductTypeEnum.DIGITAL:
          response.advancedInfo = {
            purchaseCount: advancedInfo.purchaseCount,
            digitalFulfillmentFlow: advancedInfo.digitalFulfillmentFlow,
            digitalOutput: advancedInfo.digitalOutput,
          };
          break;

        case ProductTypeEnum.COMBO:
          response.advancedInfo = {
            purchaseCount: advancedInfo.purchaseCount,
            combo: advancedInfo.combo,
          };
          break;

        default:
          response.advancedInfo = {
            purchaseCount: advancedInfo.purchaseCount,
          };
      }
    } catch (advancedInfoError) {
      this.logger.warn(
        `Không thể lấy advanced info cho response: ${advancedInfoError.message}`,
      );
    }
  }

  /**
   * Thêm upload URLs vào response
   */
  private addUploadUrlsToResponse(
    response: any,
    productId: number,
    imagesUploadUrls: any[],
    advancedImagesUploadUrls: any[],
    classificationUploadUrls: any[],
  ): void {
    if (
      imagesUploadUrls.length > 0 ||
      advancedImagesUploadUrls.length > 0 ||
      classificationUploadUrls.length > 0
    ) {
      response.uploadUrls = {
        productId: productId.toString(),
        ...(imagesUploadUrls.length > 0 ? { imagesUploadUrls } : {}),
        ...(advancedImagesUploadUrls.length > 0
          ? { advancedImagesUploadUrls }
          : {}),
        ...(classificationUploadUrls.length > 0
          ? { classificationUploadUrls }
          : {}),
      };
    }
  }

  /**
   * Thêm inventory vào response
   */
  private async addInventoryToResponse(
    product: UserProduct,
    response: any,
    inventory?: InventoryResponseDto,
  ): Promise<void> {
    if (inventory) {
      response.inventory = inventory;
      return;
    }

    // Chỉ PHYSICAL products cần inventory
    if (product.productType !== ProductTypeEnum.PHYSICAL) {
      return;
    }

    // Lấy inventory từ database như API GET
    try {
      const inventoriesResult = await this.inventoryRepository.findAll({
        productId: product.id,
        limit: 1,
      });

      if (inventoriesResult.items && inventoriesResult.items.length > 0) {
        const existingInventory = inventoriesResult.items[0];
        const inventoryDto = plainToInstance(
          InventoryResponseDto,
          existingInventory,
          {
            excludeExtraneousValues: true,
          },
        );

        // Lấy thông tin warehouse nếu có
        await this.addWarehouseToInventory(
          inventoryDto,
          existingInventory.warehouseId,
        );
        response.inventory = inventoryDto;
      }
    } catch (inventoryError) {
      this.logger.warn(
        `Không thể lấy thông tin tồn kho cho sản phẩm ${product.id}: ${inventoryError.message}`,
      );
    }
  }

  /**
   * Thêm thông tin warehouse vào inventory
   */
  private async addWarehouseToInventory(
    inventoryDto: InventoryResponseDto,
    warehouseId?: number,
  ): Promise<void> {
    if (!warehouseId) {
      return;
    }

    try {
      const warehouse =
        await this.physicalWarehouseRepository.findByWarehouseIdWithDetails(
          warehouseId,
        );
      if (warehouse) {
        inventoryDto.warehouse = {
          id: warehouse.id,
          warehouseId: warehouse.warehouseId,
          name: warehouse.name,
          description: warehouse.description,
          type: warehouse.type,
          address: warehouse.address,
          capacity: warehouse.capacity,
        };
      }
    } catch (warehouseError) {
      this.logger.warn(
        `Không thể lấy thông tin warehouse cho inventory: ${warehouseError.message}`,
      );
    }
  }

  // Placeholder methods - sẽ được implement từ service gốc
  private async createAdvancedImagesUploadUrls(
    advancedInfo: any,
    productType: any,
    now: number,
  ): Promise<any[]> {
    // TODO: Implement từ service gốc
    return [];
  }

  private async updateAdvancedInfo(
    productId: number,
    productType: any,
    advancedInfo: any,
  ): Promise<any> {
    // TODO: Implement từ service gốc
    return null;
  }

  private async updateAdvancedInfoWithImageKeys(
    product: UserProduct,
    advancedInfo: any,
    uploadUrls: any[],
  ): Promise<void> {
    // TODO: Implement từ service gốc
  }

  private async useExistingInventory(
    productId: number,
    inventoryId: number,
    userId: number,
  ): Promise<InventoryResponseDto> {
    // TODO: Implement từ service gốc
    throw new Error('Method not implemented');
  }

  private async createOrUpdateProductInventory(
    productId: number,
    inventoryData: ProductInventoryDto,
    userId: number,
  ): Promise<InventoryResponseDto> {
    // TODO: Implement từ service gốc
    throw new Error('Method not implemented');
  }

  private async processTicketTypesWithImages(
    ticketTypes: any[],
  ): Promise<any[]> {
    // TODO: Implement từ service gốc
    return ticketTypes;
  }

  private async processServicePackagesWithImages(
    servicePackages: any[],
  ): Promise<any[]> {
    // TODO: Implement từ service gốc
    return servicePackages;
  }
}
